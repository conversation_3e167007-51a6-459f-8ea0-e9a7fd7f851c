<template>
  <div class="home-page">
    <!-- 轮播图部分 -->
    <div class="banner-section">
      <div class="banner-slider">
        <div class="banner-slide">
          <img src="/placeholder.jpg" alt="凯丽思会员臻享" />
          <div class="banner-content fade-in-left">
            <h2 class="delay-200">会员臻享</h2>
            <h3 class="delay-400">为美闪耀</h3>
            <p class="delay-600">上市10周年 会员奢宠季</p>
          </div>
        </div>
      </div>
      <div class="banner-controls fade-in-up delay-800">
        <button class="prev-btn">
          <span>&lt;</span>
        </button>
        <button class="next-btn">
          <span>&gt;</span>
        </button>
      </div>
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav">
      <div class="container">
        <div class="quick-nav-grid">
          <div class="quick-nav-item reveal-on-scroll fade-in-up" data-once="true">
            <img src="/placeholder.jpg" alt="面部整形" />
            <h3>面部整形</h3>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-100" data-once="true">
            <img src="/placeholder.jpg" alt="眼部整形" />
            <h3>眼部整形</h3>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-200" data-once="true">
            <img src="/placeholder.jpg" alt="鼻部整形" />
            <h3>鼻部整形</h3>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-300" data-once="true">
            <img src="/placeholder.jpg" alt="形体塑造" />
            <h3>形体塑造</h3>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-400" data-once="true">
            <img src="/placeholder.jpg" alt="皮肤美容" />
            <h3>皮肤美容</h3>
          </div>
        </div>
      </div>
    </div>

    <!-- 关于我们 -->
    <div class="about-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>关于凯丽思</h2>
          <p>ABOUT KAILIS</p>
        </div>
        <div class="about-content">
          <div class="about-image reveal-on-scroll fade-in-left" data-once="true">
            <img src="/placeholder.jpg" alt="凯丽思医院" />
          </div>
          <div class="about-text reveal-on-scroll fade-in-right" data-once="true">
            <h3>凯丽思医疗美容医院</h3>
            <p>凯丽思医疗美容医院是一家专注于医疗美容服务的三级整形外科医院，拥有AAAAA级医美机构资质，由中国整形美容协会评审认证。</p>
            <p>医院汇聚了国内外顶尖的医疗美容专家团队，引进国际先进的医疗设备和技术，为广大爱美人士提供安全、专业、个性化的医疗美容服务。</p>
            <p>作为上市医美企业（股票代码：835533），凯丽思始终秉承"放心医美·我选凯丽思"的服务理念，致力于让每一位顾客都能拥有自信美丽的人生。</p>
            <div class="about-stats">
              <div class="stat-item">
                <h4>15+</h4>
                <p>年行业经验</p>
              </div>
              <div class="stat-item">
                <h4>50+</h4>
                <p>专业医生</p>
              </div>
              <div class="stat-item">
                <h4>100万+</h4>
                <p>满意顾客</p>
              </div>
            </div>
            <NuxtLink to="/about" class="btn-more">了解更多</NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门项目 -->
    <div class="services-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>热门项目</h2>
          <p>POPULAR SERVICES</p>
        </div>
        <div class="services-grid">
          <div class="service-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="双眼皮手术" />
            </div>
            <div class="service-info">
              <h3>韩式精雕双眼皮</h3>
              <p>塑造自然明眸，打造灵动双眼</p>
              <NuxtLink to="/service/eye" class="btn-service">了解详情</NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="玻尿酸填充" />
            </div>
            <div class="service-info">
              <h3>玻尿酸填充</h3>
              <p>立体轮廓，年轻饱满</p>
              <NuxtLink to="/service/hyaluronic" class="btn-service">了解详情</NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-200" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="吸脂瘦身" />
            </div>
            <div class="service-info">
              <h3>精准吸脂塑形</h3>
              <p>告别顽固脂肪，塑造完美身材</p>
              <NuxtLink to="/service/liposuction" class="btn-service">了解详情</NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-300" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="肉毒素注射" />
            </div>
            <div class="service-info">
              <h3>肉毒素注射</h3>
              <p>紧致肌肤，告别皱纹</p>
              <NuxtLink to="/service/botox" class="btn-service">了解详情</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专家团队 -->
    <div class="doctors-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>专家团队</h2>
          <p>EXPERT TEAM</p>
        </div>
        <div class="doctors-slider">
          <div class="doctor-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="张教授" />
            </div>
            <div class="doctor-info">
              <h3>张教授</h3>
              <p>面部整形专家</p>
              <p>20年临床经验</p>
              <NuxtLink to="/doctors/zhang" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="李医生" />
            </div>
            <div class="doctor-info">
              <h3>李医生</h3>
              <p>眼部整形专家</p>
              <p>15年临床经验</p>
              <NuxtLink to="/doctors/li" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card reveal-on-scroll fade-in-up delay-200" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="王医生" />
            </div>
            <div class="doctor-info">
              <h3>王医生</h3>
              <p>微整形专家</p>
              <p>12年临床经验</p>
              <NuxtLink to="/doctors/wang" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户见证 -->
    <div class="testimonials-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>客户见证</h2>
          <p>TESTIMONIALS</p>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="testimonial-image">
              <img src="/placeholder.jpg" alt="客户见证" />
            </div>
            <div class="testimonial-content">
              <h3>双眼皮手术</h3>
              <div class="testimonial-text">
                <p>"在凯丽思做了韩式双眼皮，效果非常自然，恢复也很快，感谢张教授的精湛技术！"</p>
                <span>— 小李，25岁</span>
              </div>
            </div>
          </div>
          <div class="testimonial-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="testimonial-image">
              <img src="/placeholder.jpg" alt="客户见证" />
            </div>
            <div class="testimonial-content">
              <h3>玻尿酸填充</h3>
              <div class="testimonial-text">
                <p>"面部填充效果很好，整个人看起来年轻了5岁，朋友们都说我气色变好了！"</p>
                <span>— 小张，32岁</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约咨询 -->
    <div class="consultation-section gradient-bg">
      <div class="container">
        <div class="consultation-content reveal-on-scroll fade-in-scale" data-once="true">
          <h2>预约免费咨询</h2>
          <p>我们的专业顾问将为您提供一对一的个性化美丽方案</p>
          <form class="consultation-form">
            <div class="form-group">
              <input type="text" placeholder="您的姓名" />
            </div>
            <div class="form-group">
              <input type="tel" placeholder="您的电话" />
            </div>
            <div class="form-group">
              <select>
                <option value="">咨询项目</option>
                <option value="face">面部整形</option>
                <option value="eye">眼部整形</option>
                <option value="nose">鼻部整形</option>
                <option value="body">形体塑造</option>
                <option value="skin">皮肤美容</option>
              </select>
            </div>
            <button type="submit" class="btn-submit">立即预约</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.home-page {
  overflow-x: hidden;
}

/* 轮播图样式 */
.banner-section {
  position: relative;
  height: 500px;
  overflow: hidden;
}

.banner-slide {
  position: relative;
  height: 100%;
}

.banner-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.banner-content {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.banner-content h2 {
  font-size: 48px;
  margin-bottom: 10px;
}

.banner-content h3 {
  font-size: 36px;
  margin-bottom: 15px;
}

.banner-content p {
  font-size: 24px;
}

.banner-controls {
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.banner-controls button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s;
}

.banner-controls button:hover {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 快速导航 */
.quick-nav {
  padding: 40px 0;
  background-color: #f9f9f9;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 20px;
}

.quick-nav-item {
  text-align: center;
  padding: 20px;
  background-color: #fff;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.quick-nav-item:hover {
  transform: translateY(-5px);
}

.quick-nav-item img {
  width: 60px;
  height: 60px;
  margin-bottom: 15px;
}

.quick-nav-item h3 {
  font-size: 18px;
  color: #333;
}

/* 通用部分样式 */
.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.section-header p {
  font-size: 16px;
  color: #e74c3c;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 关于我们部分 */
.about-section {
  padding: 60px 0;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.about-text {
  flex: 0 0 55%;
}

.about-text h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.about-text p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.about-stats {
  display: flex;
  margin: 30px 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 10px;
}

.stat-item:not(:last-child) {
  border-right: 1px solid #ddd;
}

.stat-item h4 {
  font-size: 36px;
  color: #e74c3c;
  margin-bottom: 5px;
}

.stat-item p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.btn-more {
  display: inline-block;
  padding: 10px 30px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.btn-more:hover {
  background-color: #e74c3c;
}

/* 热门项目部分 */
.services-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.service-card {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.service-card:hover {
  transform: translateY(-5px);
}

.service-image {
  height: 200px;
  overflow: hidden;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.service-card:hover .service-image img {
  transform: scale(1.05);
}

.service-info {
  padding: 20px;
  text-align: center;
}

.service-info h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.service-info p {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.btn-service {
  display: inline-block;
  padding: 8px 20px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-service:hover {
  background-color: #e74c3c;
}

/* 专家团队部分 */
.doctors-section {
  padding: 60px 0;
}

.doctors-slider {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.doctor-card {
  flex: 0 0 calc(33.333% - 20px);
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.doctor-card:hover {
  transform: translateY(-5px);
}

.doctor-image {
  height: 300px;
  overflow: hidden;
}

.doctor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-info {
  padding: 20px;
  text-align: center;
}

.doctor-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 5px;
}

.doctor-info p {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.btn-doctor {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 20px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-doctor:hover {
  background-color: #e74c3c;
}

/* 客户见证部分 */
.testimonials-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.testimonial-card {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-image {
  flex: 0 0 40%;
}

.testimonial-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-content {
  flex: 0 0 60%;
  padding: 20px;
}

.testimonial-content h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.testimonial-text {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 10px;
  position: relative;
}

.testimonial-text:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 20px;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #f9f9f9 transparent transparent;
}

.testimonial-text p {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-bottom: 10px;
}

.testimonial-text span {
  display: block;
  text-align: right;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 预约咨询部分 */
.consultation-section {
  padding: 60px 0;
  background-color: #2c2b7b;
  color: #fff;
}

.consultation-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.consultation-content h2 {
  font-size: 32px;
  margin-bottom: 15px;
}

.consultation-content p {
  font-size: 16px;
  margin-bottom: 30px;
}

.consultation-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.form-group {
  flex: 0 0 calc(33.333% - 10px);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
}

.btn-submit {
  flex: 0 0 100%;
  margin-top: 15px;
  padding: 12px 30px;
  background-color: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-submit:hover {
  background-color: #c0392b;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .banner-content h2 {
    font-size: 36px;
  }
  
  .banner-content h3 {
    font-size: 28px;
  }
  
  .banner-content p {
    font-size: 18px;
  }
  
  .quick-nav-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .about-content {
    flex-direction: column;
  }
  
  .about-image,
  .about-text {
    flex: 0 0 100%;
  }
  
  .services-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .doctors-slider {
    flex-wrap: wrap;
  }
  
  .doctor-card {
    flex: 0 0 calc(50% - 20px);
    margin-bottom: 20px;
  }
}

@media (max-width: 768px) {
  .banner-section {
    height: 400px;
  }
  
  .testimonials-grid {
    grid-template-columns: 1fr;
  }
  
  .form-group {
    flex: 0 0 100%;
  }
}

@media (max-width: 576px) {
  .banner-content h2 {
    font-size: 28px;
  }
  
  .banner-content h3 {
    font-size: 22px;
  }
  
  .banner-content p {
    font-size: 16px;
  }
  
  .quick-nav-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .services-grid {
    grid-template-columns: 1fr;
  }
  
  .doctor-card {
    flex: 0 0 100%;
  }
  
  .testimonial-card {
    flex-direction: column;
  }
  
  .testimonial-image {
    flex: 0 0 200px;
  }
}
</style> 