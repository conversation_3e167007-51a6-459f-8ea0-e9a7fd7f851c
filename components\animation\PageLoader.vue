<template>
  <div class="page-loader" :class="{ 'fade-out': !isLoading }">
    <div class="loader-content">
      <div class="logo-animation">
        <img src="/placeholder.jpg" alt="凯丽思" />
      </div>
      <div class="loader-spinner">
        <div class="spinner"></div>
      </div>
      <h2 class="loader-text">凯丽思医疗美容</h2>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const isLoading = ref(true);

onMounted(() => {
  // 延迟关闭加载动画，给页面内容加载时间
  setTimeout(() => {
    isLoading.value = false;
    
    // 完全隐藏加载器元素
    setTimeout(() => {
      document.body.classList.add('page-loaded');
    }, 1000); // 等待淡出动画完成
  }, 1500);
});
</script>

<style scoped>
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2c2b7b, #e74c3c);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 1s ease, visibility 1s ease;
}

.fade-out {
  opacity: 0;
  visibility: hidden;
}

.loader-content {
  text-align: center;
}

.logo-animation {
  margin-bottom: 20px;
  animation: pulse 2s infinite alternate;
}

.logo-animation img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  box-shadow: 0 0 30px rgba(255, 255, 255, 0.3);
}

.loader-spinner {
  margin: 20px auto;
  width: 60px;
  height: 60px;
  position: relative;
}

.spinner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 4px solid transparent;
  border-top-color: #fff;
  animation: spin 1s linear infinite;
}

.spinner:before, .spinner:after {
  content: '';
  position: absolute;
  border-radius: 50%;
  border: 4px solid transparent;
}

.spinner:before {
  top: 5px;
  left: 5px;
  right: 5px;
  bottom: 5px;
  border-top-color: rgba(255, 255, 255, 0.8);
  animation: spin 2s linear infinite;
}

.spinner:after {
  top: 15px;
  left: 15px;
  right: 15px;
  bottom: 15px;
  border-top-color: rgba(255, 255, 255, 0.6);
  animation: spin 1.5s linear infinite;
}

.loader-text {
  color: #fff;
  font-size: 24px;
  letter-spacing: 2px;
  animation: fadeInUp 1s ease forwards;
  opacity: 0;
  transform: translateY(20px);
  animation-delay: 0.5s;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 15px rgba(255, 255, 255, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style> 