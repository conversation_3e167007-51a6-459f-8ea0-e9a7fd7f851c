<template>
  <div class="mobile-menu-container">
    <!-- 汉堡菜单按钮 -->
    <button 
      class="hamburger-btn"
      @click="toggleMenu"
      :class="{ 'active': isMenuOpen }"
      aria-label="菜单"
    >
      <span></span>
      <span></span>
      <span></span>
    </button>
    
    <!-- 移动端菜单覆盖层 -->
    <div 
      class="mobile-menu-overlay"
      :class="{ 'active': isMenuOpen }"
      @click="closeMenu"
    >
      <nav class="mobile-nav" @click.stop>
        <div class="mobile-nav-header">
          <img src="/placeholder.jpg" alt="凯丽思" class="mobile-logo" />
          <h2>凯丽思医疗美容</h2>
          <button class="close-btn" @click="closeMenu" aria-label="关闭菜单">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        
        <ul class="mobile-nav-list">
          <li>
            <NuxtLink to="/" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9,22 9,12 15,12 15,22"></polyline>
              </svg>
              <span>网站首页</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/about" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              <span>医院概况</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/doctors" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                <circle cx="9" cy="7" r="4"></circle>
                <path d="m22 21-3-3m-3 0a3 3 0 1 0 0-6 3 3 0 0 0 0 6z"></path>
              </svg>
              <span>医生团队</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/news" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path>
                <path d="M18 14h-8"></path>
                <path d="M15 18h-5"></path>
                <path d="M10 6h8v4h-8V6Z"></path>
              </svg>
              <span>新闻资讯</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/departments" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2Z"></path>
                <path d="M8 21v-5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v5"></path>
                <path d="M2 7 12 2l10 5"></path>
              </svg>
              <span>科室导航</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/qualification" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"></path>
                <polyline points="14,2 14,8 20,8"></polyline>
                <line x1="16" y1="13" x2="8" y2="13"></line>
                <line x1="16" y1="17" x2="8" y2="17"></line>
                <polyline points="10,9 9,9 8,9"></polyline>
              </svg>
              <span>医院资质</span>
            </NuxtLink>
          </li>
          <li>
            <NuxtLink to="/contact" @click="closeMenu" class="mobile-nav-link">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
              </svg>
              <span>联系我们</span>
            </NuxtLink>
          </li>
        </ul>
        
        <div class="mobile-nav-footer">
          <div class="contact-info">
            <p>咨询热线</p>
            <a href="tel:0851-8686" class="phone-link">0851-8686</a>
          </div>
          <div class="social-links">
            <button class="social-btn" title="微信咨询">
              <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M8.5 12.5c0 .5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9.9.4.9.9zm7.4 0c0 .5-.4.9-.9.9s-.9-.4-.9-.9.4-.9.9-.9.9.4.9.9z"/>
                <path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10 10-4.5 10-10S17.5 2 12 2zm0 18c-4.4 0-8-3.6-8-8s3.6-8 8-8 8 3.6 8 8-3.6 8-8 8z"/>
              </svg>
            </button>
            <button class="social-btn" title="在线预约">
              <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </button>
          </div>
        </div>
      </nav>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const isMenuOpen = ref(false);

const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value;
  
  // 防止背景滚动
  if (isMenuOpen.value) {
    document.body.style.overflow = 'hidden';
  } else {
    document.body.style.overflow = '';
  }
};

const closeMenu = () => {
  isMenuOpen.value = false;
  document.body.style.overflow = '';
};

// 监听路由变化自动关闭菜单
onMounted(() => {
  // 监听 ESC 键关闭菜单
  const handleEscape = (e) => {
    if (e.key === 'Escape' && isMenuOpen.value) {
      closeMenu();
    }
  };
  
  document.addEventListener('keydown', handleEscape);
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleEscape);
    document.body.style.overflow = '';
  });
});
</script>

<style scoped>
.mobile-menu-container {
  display: none;
}

/* 汉堡菜单按钮 */
.hamburger-btn {
  width: 40px;
  height: 40px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.hamburger-btn:hover {
  background-color: rgba(44, 43, 123, 0.1);
}

.hamburger-btn span {
  width: 24px;
  height: 3px;
  background-color: #2c2b7b;
  border-radius: 2px;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburger-btn.active span:nth-child(1) {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburger-btn.active span:nth-child(2) {
  opacity: 0;
}

.hamburger-btn.active span:nth-child(3) {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* 移动端菜单覆盖层 */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* 移动端导航 */
.mobile-nav {
  position: absolute;
  top: 0;
  right: 0;
  width: 300px;
  height: 100%;
  background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
  box-shadow: -5px 0 20px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.mobile-menu-overlay.active .mobile-nav {
  transform: translateX(0);
}

.mobile-nav-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

.mobile-logo {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.mobile-nav-header h2 {
  font-size: 16px;
  color: #2c2b7b;
  margin: 0;
  flex: 1;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: #666;
  cursor: pointer;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background-color: #f8f9fa;
  color: #e74c3c;
}

.close-btn svg {
  width: 18px;
  height: 18px;
}

/* 导航列表 */
.mobile-nav-list {
  list-style: none;
  padding: 0;
  margin: 0;
  flex: 1;
}

.mobile-nav-link {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  color: #333;
  text-decoration: none;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f0f0f0;
}

.mobile-nav-link:hover {
  background-color: #f8f9fa;
  color: #e74c3c;
}

.mobile-nav-link.router-link-active {
  background-color: #e74c3c;
  color: #fff;
}

.mobile-nav-link svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.mobile-nav-link span {
  font-size: 15px;
  font-weight: 500;
}

/* 导航底部 */
.mobile-nav-footer {
  padding: 20px;
  border-top: 1px solid #e9ecef;
  background-color: #f8f9fa;
}

.contact-info {
  text-align: center;
  margin-bottom: 15px;
}

.contact-info p {
  font-size: 13px;
  color: #666;
  margin: 0 0 5px 0;
}

.phone-link {
  font-size: 18px;
  font-weight: 600;
  color: #e74c3c;
  text-decoration: none;
}

.social-links {
  display: flex;
  justify-content: center;
  gap: 15px;
}

.social-btn {
  width: 40px;
  height: 40px;
  border: none;
  background: #e74c3c;
  color: #fff;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.social-btn:hover {
  background: #c0392b;
  transform: scale(1.1);
}

.social-btn svg {
  width: 20px;
  height: 20px;
}

/* 响应式显示 */
@media (max-width: 768px) {
  .mobile-menu-container {
    display: block;
  }
}

@media (max-width: 576px) {
  .mobile-nav {
    width: 280px;
  }
  
  .mobile-nav-header {
    padding: 15px;
  }
  
  .mobile-nav-link {
    padding: 12px 15px;
  }
  
  .mobile-nav-footer {
    padding: 15px;
  }
}
</style>
