<template>
  <div class="about-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1>医院概况</h1>
        <div class="breadcrumb">
          <NuxtLink to="/">首页</NuxtLink> &gt; <span>医院概况</span>
        </div>
      </div>
    </div>

    <!-- 医院介绍 -->
    <div class="hospital-intro">
      <div class="container">
        <div class="section-header">
          <h2>凯丽思医疗美容医院</h2>
          <p>KAILIS MEDICAL BEAUTY HOSPITAL</p>
        </div>
        <div class="intro-content">
          <div class="intro-image">
            <img src="/placeholder.jpg" alt="凯丽思医院外观" />
          </div>
          <div class="intro-text">
            <p>凯丽思医疗美容医院成立于2010年，是一家专注于医疗美容服务的三级整形外科医院，拥有AAAAA级医美机构资质，由中国整形美容协会评审认证。</p>
            <p>医院位于贵州省贵阳市南明区花果园金融街1号，占地面积5000平方米，拥有先进的医疗设备和舒适的就医环境。</p>
            <p>作为上市医美企业（股票代码：835533），凯丽思始终秉承"放心医美·我选凯丽思"的服务理念，致力于让每一位顾客都能拥有自信美丽的人生。</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 医院优势 -->
    <div class="hospital-advantages">
      <div class="container">
        <div class="section-header">
          <h2>我们的优势</h2>
          <p>OUR ADVANTAGES</p>
        </div>
        <div class="advantages-grid">
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="资质认证" />
            </div>
            <h3>权威资质</h3>
            <p>三级整形外科医院<br />AAAAA级医美机构</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="专家团队" />
            </div>
            <h3>专家团队</h3>
            <p>国内外顶尖医美专家<br />平均15年以上临床经验</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="先进技术" />
            </div>
            <h3>先进技术</h3>
            <p>引进国际先进医疗设备<br />采用前沿医美技术</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="安全保障" />
            </div>
            <h3>安全保障</h3>
            <p>严格执行医疗规范<br />全程无菌操作</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="贴心服务" />
            </div>
            <h3>贴心服务</h3>
            <p>一对一私人定制<br />全程专业陪护</p>
          </div>
          <div class="advantage-card">
            <div class="advantage-icon">
              <img src="/placeholder.jpg" alt="售后保障" />
            </div>
            <h3>售后保障</h3>
            <p>术后定期回访<br />终身维护跟踪</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 医院环境 -->
    <div class="hospital-environment">
      <div class="container">
        <div class="section-header">
          <h2>医院环境</h2>
          <p>HOSPITAL ENVIRONMENT</p>
        </div>
        <div class="environment-gallery">
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="医院前台" />
            <p>医院前台</p>
          </div>
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="咨询室" />
            <p>咨询室</p>
          </div>
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="手术室" />
            <p>手术室</p>
          </div>
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="休息区" />
            <p>休息区</p>
          </div>
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="VIP病房" />
            <p>VIP病房</p>
          </div>
          <div class="gallery-item">
            <img src="/placeholder.jpg" alt="恢复室" />
            <p>恢复室</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 发展历程 -->
    <div class="hospital-history">
      <div class="container">
        <div class="section-header">
          <h2>发展历程</h2>
          <p>OUR HISTORY</p>
        </div>
        <div class="timeline">
          <div class="timeline-item">
            <div class="timeline-year">2010</div>
            <div class="timeline-content">
              <h3>凯丽思医疗美容医院成立</h3>
              <p>在贵阳市中心成立第一家医疗美容门诊</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2013</div>
            <div class="timeline-content">
              <h3>获得三级整形外科医院资质</h3>
              <p>成为贵州省首家三级整形外科医院</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2015</div>
            <div class="timeline-content">
              <h3>上市成功</h3>
              <p>成功登陆资本市场，股票代码：835533</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2018</div>
            <div class="timeline-content">
              <h3>获得AAAAA级医美机构认证</h3>
              <p>通过中国整形美容协会严格评审</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2020</div>
            <div class="timeline-content">
              <h3>新院区启用</h3>
              <p>搬迁至花果园金融街新院区，规模扩大至5000平方米</p>
            </div>
          </div>
          <div class="timeline-item">
            <div class="timeline-year">2023</div>
            <div class="timeline-content">
              <h3>服务顾客突破100万</h3>
              <p>累计服务顾客突破100万人次，成为西南地区最具影响力的医美品牌</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 联系我们 -->
    <div class="contact-section">
      <div class="container">
        <div class="section-header">
          <h2>联系我们</h2>
          <p>CONTACT US</p>
        </div>
        <div class="contact-info">
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="地址" />
            </div>
            <h3>医院地址</h3>
            <p>贵州省贵阳市南明区花果园金融街1号</p>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="电话" />
            </div>
            <h3>咨询热线</h3>
            <p>0851-8686</p>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="时间" />
            </div>
            <h3>营业时间</h3>
            <p>周一至周日 9:00-21:00</p>
          </div>
        </div>
        <div class="hospital-map">
          <img src="/placeholder.jpg" alt="医院地图" />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.about-page {
  overflow-x: hidden;
}

.page-header {
  background-color: #2c2b7b;
  color: #fff;
  padding: 40px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb a {
  color: #fff;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.section-header p {
  font-size: 16px;
  color: #e74c3c;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 医院介绍部分 */
.hospital-intro {
  padding: 60px 0;
}

.intro-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.intro-image {
  flex: 0 0 45%;
}

.intro-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.intro-text {
  flex: 0 0 55%;
}

.intro-text p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.8;
}

/* 医院优势部分 */
.hospital-advantages {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.advantages-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.advantage-card {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.advantage-card:hover {
  transform: translateY(-5px);
}

.advantage-icon {
  margin-bottom: 20px;
}

.advantage-icon img {
  width: 60px;
  height: 60px;
}

.advantage-card h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
}

.advantage-card p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

/* 医院环境部分 */
.hospital-environment {
  padding: 60px 0;
}

.environment-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: transform 0.3s;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-item p {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  padding: 10px;
  margin: 0;
  text-align: center;
  font-size: 14px;
}

/* 发展历程部分 */
.hospital-history {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.timeline {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

.timeline:before {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  left: 50%;
  width: 2px;
  background-color: #ddd;
  transform: translateX(-50%);
}

.timeline-item {
  position: relative;
  margin-bottom: 30px;
  display: flex;
  align-items: center;
}

.timeline-item:nth-child(odd) {
  flex-direction: row-reverse;
}

.timeline-year {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  background-color: #2c2b7b;
  color: #fff;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: bold;
  z-index: 1;
}

.timeline-content {
  width: 45%;
  background-color: #fff;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.timeline-content h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 10px;
}

.timeline-content p {
  font-size: 14px;
  color: #666;
}

/* 联系我们部分 */
.contact-section {
  padding: 60px 0;
}

.contact-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
}

.info-card {
  flex: 0 0 30%;
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.info-icon {
  margin-bottom: 20px;
}

.info-icon img {
  width: 50px;
  height: 50px;
}

.info-card h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 10px;
}

.info-card p {
  font-size: 16px;
  color: #666;
}

.hospital-map {
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hospital-map img {
  width: 100%;
  display: block;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .intro-content {
    flex-direction: column;
  }
  
  .intro-image,
  .intro-text {
    flex: 0 0 100%;
  }
  
  .advantages-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .environment-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .timeline:before {
    left: 40px;
  }
  
  .timeline-item,
  .timeline-item:nth-child(odd) {
    flex-direction: row;
  }
  
  .timeline-year {
    left: 40px;
    width: 60px;
    height: 60px;
    font-size: 16px;
  }
  
  .timeline-content {
    width: calc(100% - 80px);
    margin-left: 80px;
  }
  
  .contact-info {
    flex-direction: column;
    gap: 20px;
  }
  
  .info-card {
    flex: 0 0 100%;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 28px;
  }
  
  .advantages-grid {
    grid-template-columns: 1fr;
  }
  
  .environment-gallery {
    grid-template-columns: 1fr;
  }
}
</style> 