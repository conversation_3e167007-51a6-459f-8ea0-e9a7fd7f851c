<template>
  <div class="fab-container" :class="{ 'fab-open': isOpen }">
    <!-- 主按钮 -->
    <button 
      class="fab-main ripple" 
      @click="toggleFab"
      :aria-expanded="isOpen"
      aria-label="浮动操作菜单"
    >
      <svg 
        class="fab-icon" 
        :class="{ 'rotated': isOpen }"
        viewBox="0 0 24 24" 
        fill="none" 
        stroke="currentColor" 
        stroke-width="2"
      >
        <line x1="12" y1="5" x2="12" y2="19"></line>
        <line x1="5" y1="12" x2="19" y2="12"></line>
      </svg>
    </button>
    
    <!-- 子按钮 -->
    <div class="fab-actions">
      <button 
        class="fab-action ripple" 
        @click="scrollToTop"
        title="回到顶部"
        :style="{ transitionDelay: '0.1s' }"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="18,15 12,9 6,15"></polyline>
        </svg>
      </button>
      
      <button 
        class="fab-action ripple" 
        @click="openConsultation"
        title="在线咨询"
        :style="{ transitionDelay: '0.2s' }"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z"></path>
        </svg>
      </button>
      
      <button 
        class="fab-action ripple" 
        @click="makeCall"
        title="拨打电话"
        :style="{ transitionDelay: '0.3s' }"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
        </svg>
      </button>
      
      <button 
        class="fab-action ripple" 
        @click="openAppointment"
        title="预约挂号"
        :style="{ transitionDelay: '0.4s' }"
      >
        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
          <line x1="16" y1="2" x2="16" y2="6"></line>
          <line x1="8" y1="2" x2="8" y2="6"></line>
          <line x1="3" y1="10" x2="21" y2="10"></line>
        </svg>
      </button>
    </div>
    
    <!-- 背景遮罩 -->
    <div 
      class="fab-backdrop" 
      :class="{ 'visible': isOpen }"
      @click="closeFab"
    ></div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const isOpen = ref(false);

const toggleFab = () => {
  isOpen.value = !isOpen.value;
};

const closeFab = () => {
  isOpen.value = false;
};

const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  });
  closeFab();
};

const openConsultation = () => {
  // 这里可以打开在线咨询窗口
  console.log('打开在线咨询');
  closeFab();
};

const makeCall = () => {
  window.location.href = 'tel:0851-8686';
  closeFab();
};

const openAppointment = () => {
  // 这里可以跳转到预约页面
  console.log('打开预约页面');
  closeFab();
};
</script>

<style scoped>
.fab-container {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 1000;
}

.fab-main {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  border: none;
  color: #fff;
  cursor: pointer;
  box-shadow: 0 4px 20px rgba(231, 76, 60, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1001;
}

.fab-main:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 25px rgba(231, 76, 60, 0.6);
}

.fab-icon {
  width: 24px;
  height: 24px;
  transition: transform 0.3s ease;
}

.fab-icon.rotated {
  transform: rotate(45deg);
}

.fab-actions {
  position: absolute;
  bottom: 70px;
  right: 0;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.fab-action {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.95);
  border: none;
  color: #333;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transform: scale(0) translateY(20px);
  opacity: 0;
}

.fab-open .fab-action {
  transform: scale(1) translateY(0);
  opacity: 1;
}

.fab-action:hover {
  transform: scale(1.1) translateY(0);
  background: #fff;
  color: #e74c3c;
}

.fab-action svg {
  width: 20px;
  height: 20px;
}

.fab-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  z-index: 999;
}

.fab-backdrop.visible {
  opacity: 1;
  visibility: visible;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fab-container {
    bottom: 20px;
    right: 20px;
  }
  
  .fab-main {
    width: 55px;
    height: 55px;
  }
  
  .fab-action {
    width: 45px;
    height: 45px;
  }
  
  .fab-actions {
    bottom: 65px;
  }
}
</style>
