# 凯丽思医疗美容医院网站

这是一个使用Nuxt 3构建的医疗美容医院网站，为凯丽思医疗美容医院提供在线展示和服务平台。

## 网站功能

- 首页：展示医院概况、热门项目、专家团队和客户见证
- 医院概况：详细介绍医院背景、优势、环境和发展历程
- 医生团队：展示医院专家团队成员及其专业领域
- 联系我们：提供医院联系方式、地图导航、在线留言和常见问题解答

## 技术栈

- Nuxt 3：Vue 3框架，提供SSR（服务端渲染）能力
- Vue 3：前端框架
- Vue Router：路由管理
- 响应式设计：适配移动端和桌面端

## 项目结构

```
kailis/
├── .nuxt/               # Nuxt生成的文件
├── assets/              # 静态资源文件夹
│   └── images/          # 图片资源
├── components/          # 组件文件夹
├── layouts/             # 布局文件夹
│   └── default.vue      # 默认布局
├── pages/               # 页面文件夹
│   ├── index.vue        # 首页
│   ├── about.vue        # 医院概况页
│   ├── doctors.vue      # 医生团队页
│   └── contact.vue      # 联系我们页
├── public/              # 公共资源文件夹
├── app.vue              # 应用入口
├── nuxt.config.ts       # Nuxt配置文件
└── package.json         # 项目依赖
```

## 安装和运行

确保已安装依赖：

```bash
# npm
npm install

# yarn
yarn install

# pnpm
pnpm install
```

开发服务器运行（http://localhost:3000）：

```bash
# npm
npm run dev

# yarn
yarn dev

# pnpm
pnpm dev
```

## 构建生产版本

```bash
# npm
npm run build

# yarn
yarn build

# pnpm
pnpm build
```

## 更新记录

### 2023-06-08
- 创建项目基础结构
- 实现默认布局（包含页面头部、导航栏和页脚）
- 开发首页，包含轮播图、快速导航、关于我们、热门项目、专家团队、客户见证和预约咨询模块
- 开发医院概况页面，包含医院介绍、医院优势、医院环境、发展历程和联系信息模块
- 开发医生团队页面，展示专家团队成员信息和预约咨询功能
- 开发联系我们页面，提供联系方式、地图导航、在线留言和常见问题解答
- 实现全站响应式布局，适配移动端和桌面端浏览器

### 2024-07-XX 

- 添加了开场动画和页面过渡动画
- 添加了页面元素的滚动显示动画
- 优化了首页的视觉效果
- 创建了动画相关组件和CSS工具类

### 2024-07-XX 

- 添加了Docker支持
- 创建了Dockerfile和docker-compose配置
- 优化了移动端显示效果

## 注意事项

- 网站主要运行在移动端iOS和安卓的浏览器上，已针对移动端进行优化
- 所有图片资源需放置在public目录下才能正确访问
- FAQ功能使用了客户端JavaScript，确保在mounted钩子中执行
