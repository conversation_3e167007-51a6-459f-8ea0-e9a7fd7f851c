/* 动画工具类 */

/* 淡入动画 */
.fade-in {
  animation: fadeIn 1s ease forwards;
}

/* 从下方淡入 */
.fade-in-up {
  animation: fadeInUp 1s ease forwards;
}

/* 从上方淡入 */
.fade-in-down {
  animation: fadeInDown 1s ease forwards;
}

/* 从左侧淡入 */
.fade-in-left {
  animation: fadeInLeft 1s ease forwards;
}

/* 从右侧淡入 */
.fade-in-right {
  animation: fadeInRight 1s ease forwards;
}

/* 缩放淡入 */
.fade-in-scale {
  animation: fadeInScale 1s ease forwards;
}

/* 延迟类 */
.delay-100 {
  animation-delay: 0.1s;
}

.delay-200 {
  animation-delay: 0.2s;
}

.delay-300 {
  animation-delay: 0.3s;
}

.delay-400 {
  animation-delay: 0.4s;
}

.delay-500 {
  animation-delay: 0.5s;
}

.delay-600 {
  animation-delay: 0.6s;
}

.delay-700 {
  animation-delay: 0.7s;
}

.delay-800 {
  animation-delay: 0.8s;
}

.delay-900 {
  animation-delay: 0.9s;
}

.delay-1000 {
  animation-delay: 1s;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 悬浮效果 */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 闪烁动画 */
.blink {
  animation: blink 1.5s infinite;
}

@keyframes blink {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 旋转动画 */
.rotate {
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 摇晃动画 */
.shake {
  animation: shake 0.82s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes shake {
  10%, 90% {
    transform: translate3d(-1px, 0, 0);
  }
  
  20%, 80% {
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    transform: translate3d(4px, 0, 0);
  }
}

/* 弹跳动画 */
.bounce {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20px);
  }
  60% {
    transform: translateY(-10px);
  }
}

/* 渐变背景动画 */
.gradient-bg {
  background: linear-gradient(-45deg, #2c2b7b, #9b59b6, #e74c3c, #f1c40f);
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* 滚动显示动画类 */
.reveal-on-scroll {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.8s ease, transform 0.8s ease;
}

.reveal-on-scroll.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* 新增高级动画效果 */

/* 波纹效果 */
.ripple {
  position: relative;
  overflow: hidden;
  cursor: pointer;
}

.ripple::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple:active::before {
  width: 300px;
  height: 300px;
}

/* 磁性悬停效果 */
.magnetic {
  transition: transform 0.3s ease;
}

.magnetic:hover {
  transform: scale(1.05);
}

/* 霓虹灯效果 */
.neon {
  color: #fff;
  text-shadow:
    0 0 5px currentColor,
    0 0 10px currentColor,
    0 0 15px currentColor,
    0 0 20px #e74c3c,
    0 0 35px #e74c3c,
    0 0 40px #e74c3c;
  animation: neonFlicker 2s infinite alternate;
}

@keyframes neonFlicker {
  0%, 100% {
    text-shadow:
      0 0 5px currentColor,
      0 0 10px currentColor,
      0 0 15px currentColor,
      0 0 20px #e74c3c,
      0 0 35px #e74c3c,
      0 0 40px #e74c3c;
  }
  50% {
    text-shadow:
      0 0 2px currentColor,
      0 0 5px currentColor,
      0 0 8px currentColor,
      0 0 12px #e74c3c,
      0 0 18px #e74c3c,
      0 0 25px #e74c3c;
  }
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid #e74c3c;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: #e74c3c; }
}

/* 3D翻转效果 */
.flip-3d {
  perspective: 1000px;
}

.flip-3d-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.flip-3d:hover .flip-3d-inner {
  transform: rotateY(180deg);
}

.flip-3d-front, .flip-3d-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
}

.flip-3d-back {
  transform: rotateY(180deg);
}

/* 浮动动画变体 */
.float-slow {
  animation: floatSlow 6s ease-in-out infinite;
}

.float-fast {
  animation: floatFast 2s ease-in-out infinite;
}

@keyframes floatSlow {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes floatFast {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

/* 呼吸效果 */
.breathe {
  animation: breathe 4s ease-in-out infinite;
}

@keyframes breathe {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* 滑入效果 */
.slide-in-left {
  animation: slideInLeft 1s ease forwards;
}

.slide-in-right {
  animation: slideInRight 1s ease forwards;
}

.slide-in-top {
  animation: slideInTop 1s ease forwards;
}

.slide-in-bottom {
  animation: slideInBottom 1s ease forwards;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInTop {
  from {
    opacity: 0;
    transform: translateY(-100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInBottom {
  from {
    opacity: 0;
    transform: translateY(100px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 弹性动画 */
.elastic {
  animation: elastic 2s ease-out;
}

@keyframes elastic {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  75% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
  }
}

/* 橡皮筋效果 */
.rubber-band {
  animation: rubberBand 1s ease-out;
}

@keyframes rubberBand {
  0% {
    transform: scale(1);
  }
  30% {
    transform: scaleX(1.25) scaleY(0.75);
  }
  40% {
    transform: scaleX(0.75) scaleY(1.25);
  }
  50% {
    transform: scaleX(1.15) scaleY(0.85);
  }
  65% {
    transform: scaleX(0.95) scaleY(1.05);
  }
  75% {
    transform: scaleX(1.05) scaleY(0.95);
  }
  100% {
    transform: scale(1);
  }
}

/* 心跳效果 */
.heartbeat {
  animation: heartbeat 1.5s ease-in-out infinite;
}

@keyframes heartbeat {
  0%, 50%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.1);
  }
}

/* 摆动效果 */
.swing {
  animation: swing 2s ease-in-out infinite;
  transform-origin: top center;
}

@keyframes swing {
  20% {
    transform: rotate(15deg);
  }
  40% {
    transform: rotate(-10deg);
  }
  60% {
    transform: rotate(5deg);
  }
  80% {
    transform: rotate(-5deg);
  }
  100% {
    transform: rotate(0deg);
  }
}

/* 翻转效果 */
.flip {
  animation: flip 1s ease-in-out;
}

@keyframes flip {
  0% {
    transform: perspective(400px) rotateY(0);
  }
  40% {
    transform: perspective(400px) translateZ(150px) rotateY(170deg);
  }
  50% {
    transform: perspective(400px) translateZ(150px) rotateY(190deg) scale(1);
  }
  80% {
    transform: perspective(400px) rotateY(360deg) scale(0.95);
  }
  100% {
    transform: perspective(400px) scale(1);
  }
}

/* 光晕效果 */
.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px rgba(231, 76, 60, 0.5);
  }
  to {
    box-shadow: 0 0 20px rgba(231, 76, 60, 0.8), 0 0 30px rgba(231, 76, 60, 0.6);
  }
}

/* 彩虹文字效果 */
.rainbow-text {
  background: linear-gradient(45deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3);
  background-size: 400% 400%;
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  animation: rainbow 3s ease infinite;
}

@keyframes rainbow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 粒子效果容器 */
.particles-container {
  position: relative;
  overflow: hidden;
}

.particles-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 200, 255, 0.3) 0%, transparent 50%);
  animation: particleFloat 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes particleFloat {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 1;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 0.8;
  }
}