<template>
  <div class="page-loader" :class="{ 'fade-out': !isLoading }">
    <!-- 粒子背景 -->
    <div class="particles">
      <div class="particle" v-for="i in 50" :key="i" :style="getParticleStyle(i)"></div>
    </div>

    <!-- 主要内容 -->
    <div class="loader-content">
      <div class="logo-container">
        <div class="logo-animation">
          <img src="/placeholder.jpg" alt="凯丽思" />
          <div class="logo-glow"></div>
        </div>
        <div class="logo-rings">
          <div class="ring ring-1"></div>
          <div class="ring ring-2"></div>
          <div class="ring ring-3"></div>
        </div>
      </div>

      <div class="loader-spinner">
        <div class="spinner-container">
          <div class="spinner"></div>
          <div class="spinner-inner"></div>
        </div>
      </div>

      <div class="text-container">
        <h2 class="loader-text">凯丽思医疗美容</h2>
        <p class="loader-subtitle">专业 · 安全 · 美丽</p>
        <div class="loading-dots">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>

      <!-- 进度条 -->
      <div class="progress-container">
        <div class="progress-bar" :style="{ width: progress + '%' }"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const isLoading = ref(true);
const progress = ref(0);

// 生成粒子样式
const getParticleStyle = (index) => {
  const size = Math.random() * 4 + 2;
  const left = Math.random() * 100;
  const animationDelay = Math.random() * 3;
  const animationDuration = Math.random() * 3 + 2;

  return {
    width: `${size}px`,
    height: `${size}px`,
    left: `${left}%`,
    animationDelay: `${animationDelay}s`,
    animationDuration: `${animationDuration}s`
  };
};

onMounted(() => {
  // 模拟加载进度
  const progressInterval = setInterval(() => {
    progress.value += Math.random() * 20 + 10;
    if (progress.value >= 100) {
      progress.value = 100;
      clearInterval(progressInterval);

      // 延迟关闭加载动画
      setTimeout(() => {
        isLoading.value = false;

        // 完全隐藏加载器元素
        setTimeout(() => {
          document.body.classList.add('page-loaded');
        }, 600);
      }, 200);
    }
  }, 100);
});
</script>

<style scoped>
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2c2b7b 0%, #9b59b6 25%, #e74c3c 50%, #f39c12 75%, #2c2b7b 100%);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  transition: opacity 1.5s ease, visibility 1.5s ease;
  overflow: hidden;
}

.fade-out {
  opacity: 0;
  visibility: hidden;
}

/* 粒子背景 */
.particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float linear infinite;
}

.loader-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* Logo 容器 */
.logo-container {
  position: relative;
  margin-bottom: 40px;
}

.logo-animation {
  position: relative;
  display: inline-block;
  animation: logoFloat 3s ease-in-out infinite;
}

.logo-animation img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  box-shadow: 0 0 40px rgba(255, 255, 255, 0.5);
  position: relative;
  z-index: 3;
}

.logo-glow {
  position: absolute;
  top: -10px;
  left: -10px;
  right: -10px;
  bottom: -10px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
  animation: glow 2s ease-in-out infinite alternate;
}

/* 环形动画 */
.logo-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring {
  position: absolute;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.ring-1 {
  width: 140px;
  height: 140px;
  animation: ringPulse 2s ease-in-out infinite;
}

.ring-2 {
  width: 160px;
  height: 160px;
  animation: ringPulse 2s ease-in-out infinite 0.5s;
}

.ring-3 {
  width: 180px;
  height: 180px;
  animation: ringPulse 2s ease-in-out infinite 1s;
}

/* 加载旋转器 */
.loader-spinner {
  margin: 20px 0;
  width: 60px;
  height: 60px;
  position: relative;
}

.spinner-container {
  position: relative;
  width: 80px;
  height: 80px;
}

.spinner {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  border: 3px solid transparent;
  border-top-color: #fff;
  border-right-color: rgba(255, 255, 255, 0.7);
  animation: spin 1.5s linear infinite;
  position: relative;
}

.spinner-inner {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  border-radius: 50%;
  border: 3px solid transparent;
  border-bottom-color: rgba(255, 255, 255, 0.8);
  border-left-color: rgba(255, 255, 255, 0.5);
  animation: spin 2s linear infinite reverse;
}

/* 文本容器 */
.text-container {
  margin-top: 20px;
}

.loader-text {
  color: #fff;
  font-size: 28px;
  font-weight: 300;
  letter-spacing: 3px;
  margin-bottom: 10px;
  animation: textGlow 2s ease-in-out infinite alternate;
}

.loader-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  letter-spacing: 2px;
  margin-bottom: 20px;
  animation: fadeInUp 1s ease forwards;
  animation-delay: 0.5s;
  opacity: 0;
}

/* 加载点动画 */
.loading-dots {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-bottom: 30px;
}

.loading-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.7);
  animation: dotBounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }
.loading-dots span:nth-child(3) { animation-delay: 0s; }

/* 进度条 */
.progress-container {
  width: 200px;
  height: 4px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 2px;
  margin: 0 auto;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.8), #fff);
  border-radius: 2px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-bar::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  animation: shimmer 1.5s infinite;
}

/* 动画关键帧 */
@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes float {
  0% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes glow {
  0% {
    opacity: 0.5;
    transform: scale(1);
  }
  100% {
    opacity: 1;
    transform: scale(1.1);
  }
}

@keyframes ringPulse {
  0% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
  50% {
    opacity: 0.5;
    transform: translate(-50%, -50%) scale(1);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(0.8);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes textGlow {
  0% {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
  }
  100% {
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8), 0 0 30px rgba(255, 255, 255, 0.6);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>