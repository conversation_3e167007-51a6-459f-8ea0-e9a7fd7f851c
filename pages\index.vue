<template>
  <div class="home-page">
    <!-- 轮播图部分 -->
    <div class="banner-section particles-container">
      <BannerSlider :slides="bannerSlides" />
    </div>

    <!-- 快速导航 -->
    <div class="quick-nav">
      <div class="container">
        <div class="quick-nav-grid">
          <div class="quick-nav-item reveal-on-scroll fade-in-up hover-lift magnetic ripple" data-once="true">
            <div class="nav-icon">
              <img src="/placeholder.jpg" alt="面部整形" />
              <div class="icon-glow"></div>
            </div>
            <h3>面部整形</h3>
            <p>专业面部轮廓塑造</p>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-100 hover-lift magnetic ripple" data-once="true">
            <div class="nav-icon">
              <img src="/placeholder.jpg" alt="眼部整形" />
              <div class="icon-glow"></div>
            </div>
            <h3>眼部整形</h3>
            <p>打造迷人双眸</p>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-200 hover-lift magnetic ripple" data-once="true">
            <div class="nav-icon">
              <img src="/placeholder.jpg" alt="鼻部整形" />
              <div class="icon-glow"></div>
            </div>
            <h3>鼻部整形</h3>
            <p>精雕立体鼻型</p>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-300 hover-lift magnetic ripple" data-once="true">
            <div class="nav-icon">
              <img src="/placeholder.jpg" alt="形体塑造" />
              <div class="icon-glow"></div>
            </div>
            <h3>形体塑造</h3>
            <p>完美身材曲线</p>
          </div>
          <div class="quick-nav-item reveal-on-scroll fade-in-up delay-400 hover-lift magnetic ripple" data-once="true">
            <div class="nav-icon">
              <img src="/placeholder.jpg" alt="皮肤美容" />
              <div class="icon-glow"></div>
            </div>
            <h3>皮肤美容</h3>
            <p>焕发肌肤光彩</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 关于我们 -->
    <div class="about-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>关于凯丽思</h2>
          <p>ABOUT KAILIS</p>
        </div>
        <div class="about-content">
          <div class="about-image reveal-on-scroll fade-in-left" data-once="true">
            <img src="/placeholder.jpg" alt="凯丽思医院" />
          </div>
          <div class="about-text reveal-on-scroll fade-in-right" data-once="true">
            <h3>凯丽思医疗美容医院</h3>
            <p>凯丽思医疗美容医院是一家专注于医疗美容服务的三级整形外科医院，拥有AAAAA级医美机构资质，由中国整形美容协会评审认证。</p>
            <p>医院汇聚了国内外顶尖的医疗美容专家团队，引进国际先进的医疗设备和技术，为广大爱美人士提供安全、专业、个性化的医疗美容服务。</p>
            <p>作为上市医美企业（股票代码：835533），凯丽思始终秉承"放心医美·我选凯丽思"的服务理念，致力于让每一位顾客都能拥有自信美丽的人生。</p>
            <div class="about-stats">
              <div class="stat-item">
                <h4>15+</h4>
                <p>年行业经验</p>
              </div>
              <div class="stat-item">
                <h4>50+</h4>
                <p>专业医生</p>
              </div>
              <div class="stat-item">
                <h4>100万+</h4>
                <p>满意顾客</p>
              </div>
            </div>
            <NuxtLink to="/about" class="btn-more">了解更多</NuxtLink>
          </div>
        </div>
      </div>
    </div>

    <!-- 热门项目 -->
    <div class="services-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>热门项目</h2>
          <p>POPULAR SERVICES</p>
        </div>
        <div class="services-grid">
          <div class="service-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="双眼皮手术" />
            </div>
            <div class="service-info">
              <h3>韩式精雕双眼皮</h3>
              <p>塑造自然明眸，打造灵动双眼</p>
              <NuxtLink to="/service/eye" class="btn-service ripple">
                <span>了解详情</span>
              </NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="玻尿酸填充" />
            </div>
            <div class="service-info">
              <h3>玻尿酸填充</h3>
              <p>立体轮廓，年轻饱满</p>
              <NuxtLink to="/service/hyaluronic" class="btn-service ripple">
                <span>了解详情</span>
              </NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-200" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="吸脂瘦身" />
            </div>
            <div class="service-info">
              <h3>精准吸脂塑形</h3>
              <p>告别顽固脂肪，塑造完美身材</p>
              <NuxtLink to="/service/liposuction" class="btn-service ripple">
                <span>了解详情</span>
              </NuxtLink>
            </div>
          </div>
          <div class="service-card reveal-on-scroll fade-in-up delay-300" data-once="true">
            <div class="service-image">
              <img src="/placeholder.jpg" alt="肉毒素注射" />
            </div>
            <div class="service-info">
              <h3>肉毒素注射</h3>
              <p>紧致肌肤，告别皱纹</p>
              <NuxtLink to="/service/botox" class="btn-service ripple">
                <span>了解详情</span>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专家团队 -->
    <div class="doctors-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>专家团队</h2>
          <p>EXPERT TEAM</p>
        </div>
        <div class="doctors-slider">
          <div class="doctor-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="张教授" />
            </div>
            <div class="doctor-info">
              <h3>张教授</h3>
              <p>面部整形专家</p>
              <p>20年临床经验</p>
              <NuxtLink to="/doctors/zhang" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="李医生" />
            </div>
            <div class="doctor-info">
              <h3>李医生</h3>
              <p>眼部整形专家</p>
              <p>15年临床经验</p>
              <NuxtLink to="/doctors/li" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card reveal-on-scroll fade-in-up delay-200" data-once="true">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="王医生" />
            </div>
            <div class="doctor-info">
              <h3>王医生</h3>
              <p>微整形专家</p>
              <p>12年临床经验</p>
              <NuxtLink to="/doctors/wang" class="btn-doctor">查看详情</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 客户见证 -->
    <div class="testimonials-section">
      <div class="container">
        <div class="section-header reveal-on-scroll fade-in-down" data-once="true">
          <h2>客户见证</h2>
          <p>TESTIMONIALS</p>
        </div>
        <div class="testimonials-grid">
          <div class="testimonial-card reveal-on-scroll fade-in-up" data-once="true">
            <div class="testimonial-image">
              <img src="/placeholder.jpg" alt="客户见证" />
            </div>
            <div class="testimonial-content">
              <h3>双眼皮手术</h3>
              <div class="testimonial-text">
                <p>"在凯丽思做了韩式双眼皮，效果非常自然，恢复也很快，感谢张教授的精湛技术！"</p>
                <span>— 小李，25岁</span>
              </div>
            </div>
          </div>
          <div class="testimonial-card reveal-on-scroll fade-in-up delay-100" data-once="true">
            <div class="testimonial-image">
              <img src="/placeholder.jpg" alt="客户见证" />
            </div>
            <div class="testimonial-content">
              <h3>玻尿酸填充</h3>
              <div class="testimonial-text">
                <p>"面部填充效果很好，整个人看起来年轻了5岁，朋友们都说我气色变好了！"</p>
                <span>— 小张，32岁</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 预约咨询 -->
    <div class="consultation-section gradient-bg">
      <div class="container">
        <div class="consultation-content reveal-on-scroll fade-in-scale" data-once="true">
          <h2>预约免费咨询</h2>
          <p>我们的专业顾问将为您提供一对一的个性化美丽方案</p>
          <form class="consultation-form">
            <div class="form-group">
              <input type="text" placeholder="您的姓名" />
            </div>
            <div class="form-group">
              <input type="tel" placeholder="您的电话" />
            </div>
            <div class="form-group">
              <select>
                <option value="">咨询项目</option>
                <option value="face">面部整形</option>
                <option value="eye">眼部整形</option>
                <option value="nose">鼻部整形</option>
                <option value="body">形体塑造</option>
                <option value="skin">皮肤美容</option>
              </select>
            </div>
            <button type="submit" class="btn-submit ripple">
              <span>立即预约</span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import BannerSlider from '~/components/BannerSlider.vue';

// 轮播图数据
const bannerSlides = [
  {
    image: '/placeholder.jpg',
    title: '会员臻享',
    subtitle: '为美闪耀',
    description: '上市10周年 会员奢宠季 专享VIP服务',
    link: '/membership',
    buttonText: '了解会员权益'
  },
  {
    image: '/placeholder.jpg',
    title: '专业医美',
    subtitle: '安全保障',
    description: 'AAAAA级医美机构 国际标准认证',
    link: '/about',
    buttonText: '了解医院'
  },
  {
    image: '/placeholder.jpg',
    title: '明星医生',
    subtitle: '精湛技艺',
    description: '汇聚国内外顶尖医美专家团队',
    link: '/doctors',
    buttonText: '查看医生'
  },
  {
    image: '/placeholder.jpg',
    title: '前沿技术',
    subtitle: '引领潮流',
    description: '引进国际先进设备与技术',
    link: '/technology',
    buttonText: '了解技术'
  }
];
</script>

<style scoped>
.home-page {
  overflow-x: hidden;
}

/* 轮播图样式 */
.banner-section {
  position: relative;
  margin-bottom: 60px;
}

/* 快速导航 */
.quick-nav {
  padding: 60px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  position: relative;
  overflow: hidden;
}

.quick-nav::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="%23ffffff" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  pointer-events: none;
}

.quick-nav-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 30px;
  position: relative;
  z-index: 2;
}

.quick-nav-item {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.1),
    0 1px 8px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.quick-nav-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease;
}

.quick-nav-item:hover::before {
  left: 100%;
}

.quick-nav-item:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.15),
    0 5px 15px rgba(0, 0, 0, 0.1);
}

.nav-icon {
  position: relative;
  display: inline-block;
  margin-bottom: 20px;
}

.nav-icon img {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  transition: transform 0.3s ease;
  position: relative;
  z-index: 2;
}

.icon-glow {
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: radial-gradient(circle, rgba(231, 76, 60, 0.3) 0%, transparent 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.quick-nav-item:hover .icon-glow {
  opacity: 1;
  animation: pulse 2s infinite;
}

.quick-nav-item:hover .nav-icon img {
  transform: scale(1.1) rotate(5deg);
}

.quick-nav-item h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 8px;
  font-weight: 600;
}

.quick-nav-item p {
  font-size: 14px;
  color: #666;
  margin: 0;
  opacity: 0.8;
}

/* 通用部分样式 */
.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.section-header p {
  font-size: 16px;
  color: #e74c3c;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 关于我们部分 */
.about-section {
  padding: 60px 0;
}

.about-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.about-image {
  flex: 0 0 45%;
}

.about-image img {
  width: 100%;
  border-radius: 10px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.about-text {
  flex: 0 0 55%;
}

.about-text h3 {
  font-size: 24px;
  color: #333;
  margin-bottom: 20px;
}

.about-text p {
  font-size: 16px;
  color: #666;
  margin-bottom: 15px;
  line-height: 1.6;
}

.about-stats {
  display: flex;
  margin: 30px 0;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 10px;
}

.stat-item:not(:last-child) {
  border-right: 1px solid #ddd;
}

.stat-item h4 {
  font-size: 36px;
  color: #e74c3c;
  margin-bottom: 5px;
}

.stat-item p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.btn-more {
  display: inline-block;
  padding: 10px 30px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  transition: background-color 0.3s;
}

.btn-more:hover {
  background-color: #e74c3c;
}

/* 热门项目部分 */
.services-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="hexagon" width="50" height="43.4" patternUnits="userSpaceOnUse"><polygon points="25,0 50,14.4 50,28.9 25,43.4 0,28.9 0,14.4" fill="none" stroke="%23ffffff" stroke-width="0.5" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23hexagon)"/></svg>');
  pointer-events: none;
}

.services-section .section-header h2 {
  color: #fff;
}

.services-section .section-header p {
  color: rgba(255, 255, 255, 0.8);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 30px;
  position: relative;
  z-index: 2;
}

.service-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  overflow: hidden;
  box-shadow:
    0 15px 35px rgba(0, 0, 0, 0.1),
    0 5px 15px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: linear-gradient(90deg, #e74c3c, #f39c12, #e74c3c);
  background-size: 200% 100%;
  animation: shimmer 3s ease-in-out infinite;
}

.service-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 10px 25px rgba(0, 0, 0, 0.1);
}

.service-image {
  height: 220px;
  overflow: hidden;
  position: relative;
}

.service-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(231, 76, 60, 0.1), rgba(44, 43, 123, 0.1));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.service-card:hover .service-image::after {
  opacity: 1;
}

.service-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-card:hover .service-image img {
  transform: scale(1.1) rotate(2deg);
}

.service-info {
  padding: 25px;
  text-align: center;
  position: relative;
}

.service-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
}

.service-info p {
  font-size: 15px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.5;
}

.btn-service {
  display: inline-block;
  padding: 12px 25px;
  background: linear-gradient(45deg, #2c2b7b, #667eea);
  color: #fff;
  text-decoration: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-service::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #e74c3c, #f39c12);
  transition: left 0.3s ease;
}

.btn-service:hover::before {
  left: 0;
}

.btn-service:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.btn-service span {
  position: relative;
  z-index: 2;
}

/* 专家团队部分 */
.doctors-section {
  padding: 60px 0;
}

.doctors-slider {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.doctor-card {
  flex: 0 0 calc(33.333% - 20px);
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.doctor-card:hover {
  transform: translateY(-5px);
}

.doctor-image {
  height: 300px;
  overflow: hidden;
}

.doctor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.doctor-info {
  padding: 20px;
  text-align: center;
}

.doctor-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 5px;
}

.doctor-info p {
  font-size: 14px;
  color: #666;
  margin-bottom: 5px;
}

.btn-doctor {
  display: inline-block;
  margin-top: 10px;
  padding: 8px 20px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-doctor:hover {
  background-color: #e74c3c;
}

/* 客户见证部分 */
.testimonials-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.testimonial-card {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.testimonial-image {
  flex: 0 0 40%;
}

.testimonial-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.testimonial-content {
  flex: 0 0 60%;
  padding: 20px;
}

.testimonial-content h3 {
  font-size: 18px;
  color: #333;
  margin-bottom: 15px;
}

.testimonial-text {
  background-color: #f9f9f9;
  padding: 15px;
  border-radius: 10px;
  position: relative;
}

.testimonial-text:before {
  content: '';
  position: absolute;
  left: -10px;
  top: 20px;
  border-width: 10px 10px 10px 0;
  border-style: solid;
  border-color: transparent #f9f9f9 transparent transparent;
}

.testimonial-text p {
  font-size: 14px;
  color: #666;
  font-style: italic;
  margin-bottom: 10px;
}

.testimonial-text span {
  display: block;
  text-align: right;
  font-size: 14px;
  color: #333;
  font-weight: 600;
}

/* 预约咨询部分 */
.consultation-section {
  padding: 80px 0;
  background: linear-gradient(135deg, #2c2b7b 0%, #e74c3c 100%);
  color: #fff;
  position: relative;
  overflow: hidden;
}

.consultation-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.consultation-content {
  max-width: 700px;
  margin: 0 auto;
  text-align: center;
  position: relative;
  z-index: 2;
}

.consultation-content h2 {
  font-size: 36px;
  margin-bottom: 20px;
  font-weight: 700;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  animation: textGlow 3s ease-in-out infinite alternate;
}

.consultation-content p {
  font-size: 18px;
  margin-bottom: 40px;
  opacity: 0.9;
  line-height: 1.6;
}

.consultation-form {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  padding: 40px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.form-group {
  flex: 0 0 calc(33.333% - 15px);
  position: relative;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  font-size: 16px;
  background: rgba(255, 255, 255, 0.9);
  color: #333;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #fff;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.form-group input::placeholder {
  color: #666;
  transition: color 0.3s ease;
}

.form-group input:focus::placeholder {
  color: #999;
}

.btn-submit {
  flex: 0 0 100%;
  margin-top: 20px;
  padding: 15px 40px;
  background: linear-gradient(45deg, #fff, #f8f9fa);
  color: #2c2b7b;
  border: none;
  border-radius: 25px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.btn-submit::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #e74c3c, #f39c12);
  transition: left 0.3s ease;
}

.btn-submit:hover::before {
  left: 0;
}

.btn-submit:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  color: #fff;
}

.btn-submit span {
  position: relative;
  z-index: 2;
}

@keyframes textGlow {
  0% {
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  }
  100% {
    text-shadow:
      0 2px 10px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(255, 255, 255, 0.5),
      0 0 30px rgba(255, 255, 255, 0.3);
  }
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .container {
    padding: 0 20px;
  }

  .quick-nav-grid {
    gap: 20px;
  }

  .services-grid {
    gap: 20px;
  }
}

@media (max-width: 992px) {
  .quick-nav {
    padding: 40px 0;
  }

  .quick-nav-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
  }

  .quick-nav-item {
    padding: 20px 15px;
  }

  .nav-icon img {
    width: 60px;
    height: 60px;
  }

  .about-content {
    flex-direction: column;
    gap: 30px;
  }

  .about-image,
  .about-text {
    flex: 0 0 100%;
  }

  .services-section {
    padding: 60px 0;
  }

  .services-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }

  .doctors-slider {
    flex-wrap: wrap;
    gap: 15px;
  }

  .doctor-card {
    flex: 0 0 calc(50% - 15px);
  }

  .consultation-section {
    padding: 60px 0;
  }

  .consultation-form {
    padding: 30px 20px;
  }
}

@media (max-width: 768px) {
  .home-page {
    padding-top: 0;
  }

  /* 快速导航移动端优化 */
  .quick-nav {
    padding: 30px 0;
  }

  .quick-nav-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .quick-nav-item {
    padding: 20px 10px;
    border-radius: 15px;
  }

  .quick-nav-item h3 {
    font-size: 16px;
    margin-bottom: 5px;
  }

  .quick-nav-item p {
    font-size: 12px;
  }

  .nav-icon img {
    width: 50px;
    height: 50px;
  }

  /* 关于我们移动端优化 */
  .about-section {
    padding: 40px 0;
  }

  .section-header h2 {
    font-size: 28px;
  }

  .about-text h3 {
    font-size: 20px;
  }

  .about-text p {
    font-size: 14px;
  }

  .about-stats {
    flex-direction: column;
    gap: 15px;
    margin: 20px 0;
  }

  .stat-item {
    border-right: none;
    border-bottom: 1px solid #ddd;
    padding-bottom: 15px;
  }

  .stat-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }

  .stat-item h4 {
    font-size: 28px;
  }

  /* 热门项目移动端优化 */
  .services-section {
    padding: 40px 0;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .service-card {
    border-radius: 15px;
  }

  .service-image {
    height: 180px;
  }

  .service-info {
    padding: 20px;
  }

  .service-info h3 {
    font-size: 18px;
  }

  .service-info p {
    font-size: 14px;
  }

  /* 专家团队移动端优化 */
  .doctors-section {
    padding: 40px 0;
  }

  .doctors-slider {
    flex-direction: column;
    gap: 20px;
  }

  .doctor-card {
    flex: 0 0 100%;
    max-width: 350px;
    margin: 0 auto;
  }

  .doctor-image {
    height: 250px;
  }

  /* 客户见证移动端优化 */
  .testimonials-section {
    padding: 40px 0;
  }

  .testimonials-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .testimonial-card {
    flex-direction: column;
    border-radius: 15px;
  }

  .testimonial-image {
    flex: 0 0 150px;
  }

  .testimonial-content {
    padding: 20px;
  }

  .testimonial-text {
    padding: 15px;
    border-radius: 10px;
  }

  .testimonial-text:before {
    display: none;
  }

  /* 预约咨询移动端优化 */
  .consultation-section {
    padding: 40px 0;
  }

  .consultation-content h2 {
    font-size: 28px;
    margin-bottom: 15px;
  }

  .consultation-content p {
    font-size: 16px;
    margin-bottom: 30px;
  }

  .consultation-form {
    padding: 25px 15px;
    border-radius: 15px;
    gap: 15px;
  }

  .form-group {
    flex: 0 0 100%;
  }

  .form-group input,
  .form-group select {
    padding: 12px 15px;
    font-size: 14px;
    border-radius: 8px;
  }

  .btn-submit {
    padding: 12px 30px;
    font-size: 16px;
    border-radius: 20px;
    margin-top: 15px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 15px;
  }

  /* 快速导航超小屏优化 */
  .quick-nav-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .quick-nav-item {
    padding: 15px 8px;
  }

  .quick-nav-item h3 {
    font-size: 14px;
  }

  .quick-nav-item p {
    font-size: 11px;
  }

  .nav-icon img {
    width: 40px;
    height: 40px;
  }

  /* 关于我们超小屏优化 */
  .section-header h2 {
    font-size: 24px;
  }

  .about-text h3 {
    font-size: 18px;
  }

  .about-text p {
    font-size: 13px;
  }

  .btn-more {
    padding: 8px 20px;
    font-size: 14px;
  }

  /* 热门项目超小屏优化 */
  .service-info h3 {
    font-size: 16px;
  }

  .service-info p {
    font-size: 13px;
  }

  .btn-service {
    padding: 8px 15px;
    font-size: 13px;
    border-radius: 15px;
  }

  /* 专家团队超小屏优化 */
  .doctor-card {
    max-width: 280px;
  }

  .doctor-image {
    height: 200px;
  }

  .doctor-info {
    padding: 15px;
  }

  .doctor-info h3 {
    font-size: 18px;
  }

  .doctor-info p {
    font-size: 13px;
  }

  .btn-doctor {
    padding: 6px 15px;
    font-size: 13px;
  }

  /* 预约咨询超小屏优化 */
  .consultation-content h2 {
    font-size: 24px;
  }

  .consultation-content p {
    font-size: 14px;
  }

  .consultation-form {
    padding: 20px 10px;
  }

  .form-group input,
  .form-group select {
    padding: 10px 12px;
    font-size: 13px;
  }

  .btn-submit {
    padding: 10px 25px;
    font-size: 14px;
  }
}

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .quick-nav-item:hover,
  .service-card:hover,
  .doctor-card:hover {
    transform: none;
  }

  .quick-nav-item:active,
  .service-card:active,
  .doctor-card:active {
    transform: scale(0.98);
    transition: transform 0.1s ease;
  }

  .btn-service:hover,
  .btn-doctor:hover,
  .btn-more:hover,
  .btn-submit:hover {
    transform: none;
  }

  .btn-service:active,
  .btn-doctor:active,
  .btn-more:active,
  .btn-submit:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }
}
</style> 