<template>
  <div class="mouse-follower">
    <!-- 鼠标跟随光标 -->
    <div 
      class="cursor-dot" 
      :style="{ 
        left: cursorPosition.x + 'px', 
        top: cursorPosition.y + 'px',
        transform: `scale(${cursorScale})`
      }"
    ></div>
    
    <!-- 鼠标跟随圆环 -->
    <div 
      class="cursor-ring" 
      :style="{ 
        left: ringPosition.x + 'px', 
        top: ringPosition.y + 'px',
        transform: `scale(${ringScale})`
      }"
    ></div>
    
    <!-- 点击波纹效果 -->
    <div 
      v-for="ripple in ripples" 
      :key="ripple.id"
      class="click-ripple"
      :style="{
        left: ripple.x + 'px',
        top: ripple.y + 'px'
      }"
    ></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const cursorPosition = ref({ x: 0, y: 0 });
const ringPosition = ref({ x: 0, y: 0 });
const cursorScale = ref(1);
const ringScale = ref(1);
const ripples = ref([]);

let animationId = null;

// 鼠标移动处理
const handleMouseMove = (e) => {
  cursorPosition.value = { x: e.clientX, y: e.clientY };
  
  // 环形光标延迟跟随
  const lerp = (start, end, factor) => start + (end - start) * factor;
  
  const updateRingPosition = () => {
    ringPosition.value.x = lerp(ringPosition.value.x, cursorPosition.value.x, 0.1);
    ringPosition.value.y = lerp(ringPosition.value.y, cursorPosition.value.y, 0.1);
    
    if (Math.abs(ringPosition.value.x - cursorPosition.value.x) > 0.1 || 
        Math.abs(ringPosition.value.y - cursorPosition.value.y) > 0.1) {
      animationId = requestAnimationFrame(updateRingPosition);
    }
  };
  
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
  updateRingPosition();
};

// 鼠标进入可交互元素
const handleMouseEnter = (e) => {
  if (e.target.matches('a, button, .clickable, [role="button"]')) {
    cursorScale.value = 1.5;
    ringScale.value = 1.5;
  }
};

// 鼠标离开可交互元素
const handleMouseLeave = (e) => {
  if (e.target.matches('a, button, .clickable, [role="button"]')) {
    cursorScale.value = 1;
    ringScale.value = 1;
  }
};

// 鼠标点击波纹效果
const handleClick = (e) => {
  const ripple = {
    id: Date.now(),
    x: e.clientX,
    y: e.clientY
  };
  
  ripples.value.push(ripple);
  
  // 1秒后移除波纹
  setTimeout(() => {
    const index = ripples.value.findIndex(r => r.id === ripple.id);
    if (index > -1) {
      ripples.value.splice(index, 1);
    }
  }, 1000);
};

onMounted(() => {
  // 检查是否为触摸设备或小屏设备
  const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  const isSmallScreen = window.innerWidth <= 768;

  if (isTouchDevice || isSmallScreen) {
    return; // 在触摸设备或小屏设备上不显示鼠标跟随效果
  }

  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseenter', handleMouseEnter, true);
  document.addEventListener('mouseleave', handleMouseLeave, true);
  document.addEventListener('click', handleClick);

  // 隐藏默认光标
  document.body.style.cursor = 'none';

  // 监听窗口大小变化
  const handleResize = () => {
    if (window.innerWidth <= 768) {
      document.body.style.cursor = 'auto';
    } else if (!isTouchDevice) {
      document.body.style.cursor = 'none';
    }
  };

  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseenter', handleMouseEnter, true);
  document.removeEventListener('mouseleave', handleMouseLeave, true);
  document.removeEventListener('click', handleClick);
  
  // 恢复默认光标
  document.body.style.cursor = 'auto';
  
  if (animationId) {
    cancelAnimationFrame(animationId);
  }
});
</script>

<style scoped>
.mouse-follower {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9998;
}

.cursor-dot {
  position: fixed;
  width: 8px;
  height: 8px;
  background-color: #e74c3c;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.2s ease;
  z-index: 9999;
}

.cursor-ring {
  position: fixed;
  width: 30px;
  height: 30px;
  border: 2px solid rgba(231, 76, 60, 0.5);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: transform 0.3s ease;
  z-index: 9998;
}

.click-ripple {
  position: fixed;
  width: 20px;
  height: 20px;
  border: 2px solid #e74c3c;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  animation: rippleExpand 1s ease-out forwards;
}

@keyframes rippleExpand {
  0% {
    width: 20px;
    height: 20px;
    opacity: 1;
  }
  100% {
    width: 100px;
    height: 100px;
    opacity: 0;
  }
}

/* 在触摸设备上隐藏 */
@media (hover: none) and (pointer: coarse) {
  .mouse-follower {
    display: none;
  }
}
</style>
