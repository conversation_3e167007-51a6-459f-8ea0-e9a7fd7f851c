<template>
  <div class="layout">
    <header class="header">
      <div class="header-top">
        <div class="container">
          <div class="logo">
            <img src="/placeholder.jpg" alt="凯丽思" />
            <div class="logo-text">
              <h1>凯丽思医疗美容</h1>
              <p>三级整形外科医院 上市医美</p>
              <p>放心医美·我选凯丽思 凯丽思股票 (835533)</p>
            </div>
          </div>
          <div class="header-right">
            <div class="certification">
              <h2>AAAAA级医美机构</h2>
              <p>评审: 中国整形美容协会</p>
            </div>
            <div class="contact">
              <img src="/placeholder.jpg" alt="电话" />
              <div>
                <p>拨打咨询热线</p>
                <h3>0851-8686</h3>
              </div>
            </div>
            <!-- 移动端菜单按钮 -->
            <MobileMenu />
          </div>
        </div>
      </div>
      <nav class="main-nav desktop-nav">
        <div class="container">
          <ul class="nav-list">
            <li><NuxtLink to="/"><span>网站首页</span></NuxtLink></li>
            <li><NuxtLink to="/about"><span>医院概况</span></NuxtLink></li>
            <li><NuxtLink to="/doctors"><span>医生团队</span></NuxtLink></li>
            <li><NuxtLink to="/news"><span>新闻资讯</span></NuxtLink></li>
            <li><NuxtLink to="/departments"><span>科室导航</span></NuxtLink></li>
            <li><NuxtLink to="/qualification"><span>医院资质</span></NuxtLink></li>
            <li><NuxtLink to="/contact"><span>联系我们</span></NuxtLink></li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="main-content">
      <slot />
    </main>

    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/placeholder.jpg" alt="凯丽思" />
            <p>凯丽思医疗美容医院</p>
          </div>
          <div class="footer-info">
            <p>地址: 贵州省贵阳市南明区花果园金融街1号</p>
            <p>电话: 0851-8686</p>
            <p>邮箱: <EMAIL></p>
          </div>
          <div class="footer-qrcode">
            <img src="/placeholder.jpg" alt="微信二维码" />
            <p>扫码关注我们</p>
          </div>
        </div>
        <div class="copyright">
          <p>© {{ new Date().getFullYear() }} 凯丽思医疗美容医院 版权所有</p>
        </div>
      </div>
    </footer>

    <!-- 浮动动作按钮 -->
    <FloatingActionButton />
  </div>
</template>

<script setup>
import FloatingActionButton from '~/components/FloatingActionButton.vue';
import MobileMenu from '~/components/MobileMenu.vue';
</script>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.header-top {
  padding: 15px 0;
}

.header-top .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 80px;
  margin-right: 15px;
}

.logo-text h1 {
  font-size: 24px;
  color: #333;
  margin-bottom: 5px;
}

.logo-text p {
  font-size: 14px;
  color: #666;
  margin: 2px 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.certification {
  margin-right: 30px;
  text-align: right;
}

.certification h2 {
  font-size: 20px;
  color: #333;
  margin-bottom: 5px;
}

.certification p {
  font-size: 14px;
  color: #666;
}

.contact {
  display: flex;
  align-items: center;
}

.contact img {
  height: 40px;
  margin-right: 10px;
}

.contact p {
  font-size: 14px;
  color: #666;
}

.contact h3 {
  font-size: 22px;
  color: #e74c3c;
}

.main-nav {
  background-color: #2c2b7b;
}

.desktop-nav {
  display: block;
}

.nav-list {
  display: flex;
  list-style: none;
  justify-content: space-between;
}

.nav-list li {
  flex: 1;
  text-align: center;
}

.nav-list a {
  display: block;
  padding: 15px 0;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-list a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, #e74c3c, #f39c12);
  transition: left 0.3s ease;
}

.nav-list a:hover::before,
.nav-list a.router-link-active::before {
  left: 0;
}

.nav-list a:hover,
.nav-list a.router-link-active {
  transform: translateY(-2px);
}

.nav-list a span {
  position: relative;
  z-index: 2;
}

.main-content {
  flex: 1;
}

.footer {
  background-color: #2c2b7b;
  color: #fff;
  padding: 40px 0 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-logo img {
  height: 60px;
  margin-bottom: 10px;
}

.footer-logo p {
  font-size: 16px;
}

.footer-info p {
  margin-bottom: 10px;
  font-size: 14px;
}

.footer-qrcode {
  text-align: center;
}

.footer-qrcode img {
  width: 120px;
  margin-bottom: 10px;
}

.copyright {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
  font-size: 14px;
}

@media (max-width: 1024px) {
  .container {
    padding: 0 20px;
  }

  .logo img {
    height: 70px;
  }

  .logo-text h1 {
    font-size: 22px;
  }

  .logo-text p {
    font-size: 13px;
  }

  .certification h2 {
    font-size: 18px;
  }

  .certification p {
    font-size: 13px;
  }

  .contact h3 {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .header {
    position: relative;
  }

  .header-top {
    padding: 10px 0;
  }

  .header-top .container {
    flex-direction: column;
    gap: 15px;
  }

  .logo {
    justify-content: center;
  }

  .logo img {
    height: 60px;
    margin-right: 10px;
  }

  .logo-text {
    text-align: center;
  }

  .logo-text h1 {
    font-size: 20px;
    margin-bottom: 3px;
  }

  .logo-text p {
    font-size: 12px;
    margin: 1px 0;
  }

  .header-right {
    flex-direction: column;
    align-items: center;
    gap: 15px;
    margin-top: 0;
  }

  .certification {
    margin-right: 0;
    text-align: center;
  }

  .certification h2 {
    font-size: 16px;
    margin-bottom: 3px;
  }

  .certification p {
    font-size: 12px;
  }

  .contact {
    justify-content: center;
  }

  .contact img {
    height: 35px;
    margin-right: 8px;
  }

  .contact h3 {
    font-size: 18px;
  }

  .contact p {
    font-size: 13px;
  }

  /* 隐藏桌面端导航 */
  .desktop-nav {
    display: none;
  }

  /* 页脚移动端优化 */
  .footer {
    padding: 30px 0 15px;
  }

  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 25px;
    margin-bottom: 25px;
  }

  .footer-logo img {
    height: 50px;
    margin-bottom: 8px;
  }

  .footer-logo p {
    font-size: 14px;
  }

  .footer-info p {
    margin-bottom: 8px;
    font-size: 13px;
  }

  .footer-qrcode img {
    width: 100px;
    margin-bottom: 8px;
  }

  .footer-qrcode p {
    font-size: 13px;
  }

  .copyright {
    padding-top: 15px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .container {
    padding: 0 15px;
  }

  .header-top {
    padding: 8px 0;
  }

  .logo img {
    height: 50px;
    margin-right: 8px;
  }

  .logo-text h1 {
    font-size: 18px;
  }

  .logo-text p {
    font-size: 11px;
  }

  .certification h2 {
    font-size: 14px;
  }

  .certification p {
    font-size: 11px;
  }

  .contact img {
    height: 30px;
    margin-right: 6px;
  }

  .contact h3 {
    font-size: 16px;
  }

  .contact p {
    font-size: 12px;
  }

  /* 导航栏超小屏优化 */
  .nav-list li {
    flex: 0 0 100%;
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .nav-list li:last-child {
    border-bottom: none;
  }

  .nav-list a {
    padding: 10px 0;
    font-size: 13px;
  }

  /* 页脚超小屏优化 */
  .footer {
    padding: 25px 0 10px;
  }

  .footer-content {
    gap: 20px;
    margin-bottom: 20px;
  }

  .footer-logo img {
    height: 45px;
  }

  .footer-logo p {
    font-size: 13px;
  }

  .footer-info p {
    font-size: 12px;
  }

  .footer-qrcode img {
    width: 90px;
  }

  .footer-qrcode p {
    font-size: 12px;
  }

  .copyright {
    font-size: 11px;
  }
}

/* 移动端触摸优化 */
@media (hover: none) and (pointer: coarse) {
  .nav-list a:hover {
    background-color: transparent;
    transform: none;
  }

  .nav-list a:active {
    background-color: rgba(231, 76, 60, 0.8);
    transform: scale(0.98);
    transition: all 0.1s ease;
  }

  .nav-list a.router-link-active {
    background-color: #e74c3c;
    transform: none;
  }
}
</style> 