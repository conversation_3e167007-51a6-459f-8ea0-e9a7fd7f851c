<template>
  <div class="banner-slider" @mouseenter="pauseAutoplay" @mouseleave="resumeAutoplay">
    <div class="slider-container">
      <!-- 轮播图片 -->
      <div 
        v-for="(slide, index) in slides" 
        :key="index"
        class="slide"
        :class="{ 
          'active': index === currentSlide,
          'prev': index === prevSlide,
          'next': index === nextSlide
        }"
      >
        <img :src="slide.image" :alt="slide.title" />
        <div class="slide-overlay"></div>
        <div class="slide-content">
          <h2 class="slide-title" :class="{ 'animate': index === currentSlide }">
            {{ slide.title }}
          </h2>
          <h3 class="slide-subtitle" :class="{ 'animate': index === currentSlide }">
            {{ slide.subtitle }}
          </h3>
          <p class="slide-description" :class="{ 'animate': index === currentSlide }">
            {{ slide.description }}
          </p>
          <NuxtLink 
            v-if="slide.link" 
            :to="slide.link" 
            class="slide-btn"
            :class="{ 'animate': index === currentSlide }"
          >
            {{ slide.buttonText || '了解更多' }}
          </NuxtLink>
        </div>
      </div>
    </div>
    
    <!-- 导航箭头 -->
    <button class="nav-btn prev-btn" @click="prevSlide" :disabled="isTransitioning">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="15,18 9,12 15,6"></polyline>
      </svg>
    </button>
    <button class="nav-btn next-btn" @click="nextSlide" :disabled="isTransitioning">
      <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polyline points="9,18 15,12 9,6"></polyline>
      </svg>
    </button>
    
    <!-- 指示器 -->
    <div class="indicators">
      <button 
        v-for="(slide, index) in slides" 
        :key="index"
        class="indicator"
        :class="{ 'active': index === currentSlide }"
        @click="goToSlide(index)"
        :disabled="isTransitioning"
      >
        <span class="sr-only">切换到第 {{ index + 1 }} 张图片</span>
      </button>
    </div>
    
    <!-- 进度条 -->
    <div class="progress-bar">
      <div 
        class="progress-fill" 
        :style="{ 
          width: progressWidth + '%',
          animationDuration: autoplayInterval + 'ms'
        }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue';

// Props
const props = defineProps({
  slides: {
    type: Array,
    default: () => [
      {
        image: '/placeholder.jpg',
        title: '会员臻享',
        subtitle: '为美闪耀',
        description: '上市10周年 会员奢宠季',
        link: '/membership',
        buttonText: '了解会员权益'
      },
      {
        image: '/placeholder.jpg',
        title: '专业医美',
        subtitle: '安全保障',
        description: 'AAAAA级医美机构 值得信赖',
        link: '/about',
        buttonText: '了解医院'
      },
      {
        image: '/placeholder.jpg',
        title: '明星医生',
        subtitle: '精湛技艺',
        description: '汇聚国内外顶尖医美专家',
        link: '/doctors',
        buttonText: '查看医生'
      }
    ]
  },
  autoplay: {
    type: Boolean,
    default: true
  },
  autoplayInterval: {
    type: Number,
    default: 5000
  }
});

// 响应式数据
const currentSlide = ref(0);
const isTransitioning = ref(false);
const isAutoplayPaused = ref(false);
const progressWidth = ref(0);

let autoplayTimer = null;
let progressTimer = null;

// 计算属性
const prevSlide = computed(() => {
  return currentSlide.value === 0 ? props.slides.length - 1 : currentSlide.value - 1;
});

const nextSlide = computed(() => {
  return currentSlide.value === props.slides.length - 1 ? 0 : currentSlide.value + 1;
});

// 方法
const goToSlide = (index) => {
  if (isTransitioning.value || index === currentSlide.value) return;
  
  isTransitioning.value = true;
  currentSlide.value = index;
  
  setTimeout(() => {
    isTransitioning.value = false;
  }, 800);
  
  resetProgress();
};

const nextSlideAction = () => {
  goToSlide(nextSlide.value);
};

const prevSlideAction = () => {
  goToSlide(prevSlide.value);
};

const startAutoplay = () => {
  if (!props.autoplay || isAutoplayPaused.value) return;
  
  autoplayTimer = setTimeout(() => {
    nextSlideAction();
    startAutoplay();
  }, props.autoplayInterval);
  
  startProgress();
};

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearTimeout(autoplayTimer);
    autoplayTimer = null;
  }
  stopProgress();
};

const pauseAutoplay = () => {
  isAutoplayPaused.value = true;
  stopAutoplay();
};

const resumeAutoplay = () => {
  isAutoplayPaused.value = false;
  startAutoplay();
};

const startProgress = () => {
  progressWidth.value = 0;
  progressTimer = setTimeout(() => {
    progressWidth.value = 100;
  }, 100);
};

const stopProgress = () => {
  if (progressTimer) {
    clearTimeout(progressTimer);
    progressTimer = null;
  }
  progressWidth.value = 0;
};

const resetProgress = () => {
  stopProgress();
  if (!isAutoplayPaused.value) {
    startProgress();
  }
};

// 生命周期
onMounted(() => {
  startAutoplay();
});

onUnmounted(() => {
  stopAutoplay();
});

// 暴露方法给父组件
defineExpose({
  nextSlide: nextSlideAction,
  prevSlide: prevSlideAction,
  goToSlide,
  pauseAutoplay,
  resumeAutoplay
});
</script>

<style scoped>
.banner-slider {
  position: relative;
  height: 600px;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.slider-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide.active {
  opacity: 1;
  transform: translateX(0);
  z-index: 2;
}

.slide.prev {
  transform: translateX(-100%);
}

.slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(44, 43, 123, 0.7), rgba(231, 76, 60, 0.5));
}

.slide-content {
  position: absolute;
  top: 50%;
  left: 10%;
  transform: translateY(-50%);
  color: #fff;
  z-index: 3;
  max-width: 50%;
}

.slide-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease 0.2s;
}

.slide-subtitle {
  font-size: 2.5rem;
  font-weight: 300;
  margin-bottom: 1rem;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease 0.4s;
}

.slide-description {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease 0.6s;
}

.slide-btn {
  display: inline-block;
  padding: 12px 30px;
  background: linear-gradient(45deg, #e74c3c, #c0392b);
  color: #fff;
  text-decoration: none;
  border-radius: 25px;
  font-weight: 600;
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s ease 0.8s, background 0.3s ease;
}

.slide-btn:hover {
  background: linear-gradient(45deg, #c0392b, #a93226);
  transform: translateY(45px) scale(1.05);
}

.slide-title.animate,
.slide-subtitle.animate,
.slide-description.animate,
.slide-btn.animate {
  opacity: 1;
  transform: translateY(0);
}

/* 导航按钮 */
.nav-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 50%;
  color: #fff;
  cursor: pointer;
  z-index: 4;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.nav-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-50%) scale(1.1);
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.prev-btn {
  left: 20px;
}

.next-btn {
  right: 20px;
}

.nav-btn svg {
  width: 24px;
  height: 24px;
}

/* 指示器 */
.indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 4;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.5);
  background: transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background: #fff;
  border-color: #fff;
}

.indicator:hover {
  border-color: #fff;
  transform: scale(1.2);
}

/* 进度条 */
.progress-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 4px;
  background: rgba(255, 255, 255, 0.2);
  z-index: 4;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #e74c3c, #f39c12);
  transition: width linear;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .banner-slider {
    height: 400px;
  }
  
  .slide-content {
    max-width: 80%;
    left: 5%;
  }
  
  .slide-title {
    font-size: 2.5rem;
  }
  
  .slide-subtitle {
    font-size: 1.8rem;
  }
  
  .slide-description {
    font-size: 1rem;
  }
  
  .nav-btn {
    width: 40px;
    height: 40px;
  }
  
  .nav-btn svg {
    width: 20px;
    height: 20px;
  }
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
