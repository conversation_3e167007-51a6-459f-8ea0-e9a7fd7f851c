<template>
  <div>
    <PageLoader />
    <MouseFollower />
    <NuxtLayout>
      <PageTransition>
        <NuxtPage />
      </PageTransition>
    </NuxtLayout>
    <ScrollReveal />
  </div>
</template>

<script setup>
import PageLoader from '~/components/animation/PageLoader.vue';
import MouseFollower from '~/components/animation/MouseFollower.vue';
import PageTransition from '~/components/animation/PageTransition.vue';
import ScrollReveal from '~/components/animation/ScrollReveal.vue';
</script>

<style>
/* 导入动画CSS */
@import '~/assets/css/animations.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
}

/* 页面加载后的样式 */
body.page-loaded {
  overflow-x: hidden;
}

/* 添加以下样式 */
img[src^="/"] {
  background-color: #cccccc;
  color: #666666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  position: relative;
}

img[src^="/"]:after {
  content: attr(alt) || attr(src);
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(204, 204, 204, 0.8);
}
</style>
