# ----------- 构建阶段 -----------
    FROM node:22-bullseye-slim AS builder

    WORKDIR /app
    
    COPY package*.json ./
    RUN npm install
    
    COPY . .
    RUN npm run build
    
    # ----------- 运行阶段 -----------
    FROM node:22-bullseye-slim AS runner
    
    WORKDIR /app
    
    COPY --from=builder /app/node_modules ./node_modules
    COPY --from=builder /app/.output ./.output
    COPY --from=builder /app/package*.json ./
    
    EXPOSE 3000
    
    CMD ["npm", "run", "start"]