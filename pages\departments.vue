<template>
  <div class="departments-page">
    <div class="page-header">
      <div class="container">
        <h1 class="reveal-on-scroll fade-in-down" data-once="true">科室导航</h1>
        <p class="reveal-on-scroll fade-in-up" data-once="true">探索我们的专业科室，了解各项前沿医美服务</p>
      </div>
    </div>

    <div class="container">
      <div class="departments-grid">
        <div 
          v-for="(dept, index) in departments" 
          :key="dept.id" 
          class="dept-card reveal-on-scroll fade-in-up" 
          :style="{ transitionDelay: `${index * 100}ms` }"
          data-once="true"
        >
          <div class="dept-icon">
            <img :src="dept.icon" :alt="dept.name" />
          </div>
          <div class="dept-info">
            <h2 class="dept-name">{{ dept.name }}</h2>
            <p class="dept-description">{{ dept.description }}</p>
            <div class="dept-features">
              <span v-for="feature in dept.features" :key="feature" class="feature-tag">{{ feature }}</span>
            </div>
            <NuxtLink :to="`/department/${dept.id}`" class="btn-dept-details ripple">
              <span>查看详情</span>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const departments = ref([
  {
    id: 'plastic-surgery',
    name: '整形外科',
    icon: '/placeholder.jpg',
    description: '提供全面的面部及身体整形手术，由资深专家团队主刀，确保安全与效果。',
    features: ['眼部整形', '鼻部整形', '胸部整形', '吸脂塑形']
  },
  {
    id: 'dermatology',
    name: '皮肤美容科',
    icon: '/placeholder.jpg',
    description: '运用国际先进的光电技术和产品，解决各种皮肤问题，焕发肌肤自然光彩。',
    features: ['光子嫩肤', '激光祛斑', '热玛吉', '皮肤管理']
  },
  {
    id: 'micro-plastic',
    name: '微整形中心',
    icon: '/placeholder.jpg',
    description: '通过注射等微创方式进行面部年轻化和轮廓塑造，恢复快，效果自然。',
    features: ['玻尿酸填充', '肉毒素除皱', '线雕提升', '胶原蛋白']
  },
  {
    id: 'dental',
    name: '美容牙科',
    icon: '/placeholder.jpg',
    description: '打造明星般璀璨笑容，提供牙齿美白、矫正、贴面等全方位牙齿美容服务。',
    features: ['冷光美白', '牙齿矫正', '全瓷贴面', '种植牙']
  },
  {
    id: 'anti-aging',
    name: '抗衰老中心',
    icon: '/placeholder.jpg',
    description: '整合内外调理方案，从细胞层面延缓衰老，为您量身定制个性化抗衰老计划。',
    features: ['荷尔蒙疗法', '干细胞抗衰', '基因检测', '全面体检']
  },
  {
    id: 'traditional-chinese-medicine',
    name: '中医美容科',
    icon: '/placeholder.jpg',
    description: '结合传统中医精髓与现代科技，提供针灸、艾灸、中药面膜等由内而外的美容调理。',
    features: ['针灸减肥', '中药祛痘', '艾灸养生', '体质调理']
  }
]);
</script>

<style scoped>
.departments-page {
  background: #f8f9fa;
  padding-bottom: 80px;
}

.page-header {
  padding: 80px 0;
  text-align: center;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: #fff;
  margin-bottom: 60px;
}

.page-header h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 15px;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
}

.departments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 30px;
}

.dept-card {
  background: #fff;
  border-radius: 20px;
  box-shadow: 0 10px 40px rgba(0,0,0,0.08);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  border-top: 4px solid #764ba2;
}

.dept-card:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 50px rgba(0,0,0,0.15);
  border-top-color: #e74c3c;
}

.dept-icon {
  background: linear-gradient(135deg, #f0f2f5, #e6e9ee);
  text-align: center;
  padding: 30px;
}

.dept-icon img {
  width: 80px;
  height: 80px;
  transition: transform 0.4s ease;
}

.dept-card:hover .dept-icon img {
  transform: scale(1.1) rotate(10deg);
}

.dept-info {
  padding: 30px;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  text-align: center;
}

.dept-name {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 15px;
}

.dept-description {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  flex-grow: 1;
}

.dept-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 25px;
}

.feature-tag {
  background: #e9ecef;
  color: #495057;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 13px;
}

.btn-dept-details {
  display: inline-block;
  padding: 12px 25px;
  background: linear-gradient(45deg, #2c2b7b, #667eea);
  color: #fff;
  text-decoration: none;
  border-radius: 25px;
  font-size: 14px;
  font-weight: 600;
  transition: all 0.3s ease;
  align-self: center;
  position: relative;
  overflow: hidden;
}

.btn-dept-details:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(44, 43, 123, 0.4);
  background: linear-gradient(45deg, #e74c3c, #f39c12);
}

.btn-dept-details span {
  position: relative;
  z-index: 2;
}
</style> 