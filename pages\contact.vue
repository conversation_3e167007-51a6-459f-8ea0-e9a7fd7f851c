<template>
  <div class="contact-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1>联系我们</h1>
        <div class="breadcrumb">
          <NuxtLink to="/">首页</NuxtLink> &gt; <span>联系我们</span>
        </div>
      </div>
    </div>

    <!-- 联系信息 -->
    <div class="contact-info-section">
      <div class="container">
        <div class="section-header">
          <h2>联系方式</h2>
          <p>CONTACT INFORMATION</p>
        </div>
        <div class="info-cards">
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="地址" />
            </div>
            <h3>医院地址</h3>
            <p>贵州省贵阳市南明区花果园金融街1号</p>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="电话" />
            </div>
            <h3>咨询热线</h3>
            <p>0851-8686</p>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="邮箱" />
            </div>
            <h3>电子邮箱</h3>
            <p><EMAIL></p>
          </div>
          <div class="info-card">
            <div class="info-icon">
              <img src="/placeholder.jpg" alt="时间" />
            </div>
            <h3>营业时间</h3>
            <p>周一至周日 9:00-21:00</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 地图和留言 -->
    <div class="map-message-section">
      <div class="container">
        <div class="map-message-content">
          <div class="map-container">
            <div class="section-header">
              <h2>位置导航</h2>
              <p>LOCATION</p>
            </div>
            <div class="map-wrapper">
              <img src="/placeholder.jpg" alt="医院地图" />
              <div class="map-info">
                <h3>交通指南</h3>
                <div class="transport-info">
                  <div class="transport-item">
                    <h4>公交路线</h4>
                    <p>1路、15路、25路、36路到花果园金融街站下车</p>
                  </div>
                  <div class="transport-item">
                    <h4>地铁路线</h4>
                    <p>地铁1号线到花果园站下车，从A出口出站步行500米</p>
                  </div>
                  <div class="transport-item">
                    <h4>自驾路线</h4>
                    <p>导航至"凯丽思医疗美容医院"或"花果园金融街1号"</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="message-container">
            <div class="section-header">
              <h2>在线留言</h2>
              <p>ONLINE MESSAGE</p>
            </div>
            <form class="message-form">
              <div class="form-group">
                <label>您的姓名</label>
                <input type="text" placeholder="请输入您的姓名" />
              </div>
              <div class="form-group">
                <label>联系电话</label>
                <input type="tel" placeholder="请输入您的联系电话" />
              </div>
              <div class="form-group">
                <label>电子邮箱</label>
                <input type="email" placeholder="请输入您的电子邮箱" />
              </div>
              <div class="form-group">
                <label>咨询项目</label>
                <select>
                  <option value="">请选择咨询项目</option>
                  <option value="face">面部整形</option>
                  <option value="eye">眼部整形</option>
                  <option value="nose">鼻部整形</option>
                  <option value="body">形体塑造</option>
                  <option value="skin">皮肤美容</option>
                  <option value="other">其他项目</option>
                </select>
              </div>
              <div class="form-group">
                <label>留言内容</label>
                <textarea placeholder="请输入您的留言内容"></textarea>
              </div>
              <button type="submit" class="btn-submit">提交留言</button>
            </form>
          </div>
        </div>
      </div>
    </div>

    <!-- 常见问题 -->
    <div class="faq-section">
      <div class="container">
        <div class="section-header">
          <h2>常见问题</h2>
          <p>FREQUENTLY ASKED QUESTIONS</p>
        </div>
        <div class="faq-list">
          <div class="faq-item">
            <div class="faq-question">
              <h3>如何预约医生咨询？</h3>
              <span class="toggle-icon">+</span>
            </div>
            <div class="faq-answer">
              <p>您可以通过以下几种方式预约医生咨询：</p>
              <ol>
                <li>拨打咨询热线0851-8686进行电话预约</li>
                <li>通过官网在线留言预约</li>
                <li>关注我们的微信公众号进行预约</li>
                <li>直接到医院前台进行现场预约</li>
              </ol>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>医院是否有免费停车场？</h3>
              <span class="toggle-icon">+</span>
            </div>
            <div class="faq-answer">
              <p>是的，我院在地下一层设有专属停车场，凭就诊凭证可免费停车3小时。</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>是否需要提前预约？</h3>
              <span class="toggle-icon">+</span>
            </div>
            <div class="faq-answer">
              <p>为了保证您的就诊体验，建议提前1-3天预约。特别是周末和节假日期间，预约时间可能较为紧张，建议提前一周预约。</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>可以使用医保卡吗？</h3>
              <span class="toggle-icon">+</span>
            </div>
            <div class="faq-answer">
              <p>很抱歉，医疗美容项目属于非医保范围，目前不能使用医保卡支付。我院支持现金、银行卡、微信、支付宝等多种支付方式。</p>
            </div>
          </div>
          <div class="faq-item">
            <div class="faq-question">
              <h3>术后是否有专人护理？</h3>
              <span class="toggle-icon">+</span>
            </div>
            <div class="faq-answer">
              <p>是的，我院所有手术项目均配有专业护理团队，提供全程一对一的专业护理服务，确保您的术后恢复顺利。</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关注我们 -->
    <div class="follow-section">
      <div class="container">
        <div class="section-header">
          <h2>关注我们</h2>
          <p>FOLLOW US</p>
        </div>
        <div class="follow-content">
          <div class="qrcode-container">
            <div class="qrcode-item">
              <img src="/placeholder.jpg" alt="微信公众号" />
              <p>微信公众号</p>
            </div>
            <div class="qrcode-item">
              <img src="/placeholder.jpg" alt="抖音号" />
              <p>抖音号</p>
            </div>
            <div class="qrcode-item">
              <img src="/placeholder.jpg" alt="小红书号" />
              <p>小红书号</p>
            </div>
          </div>
          <div class="social-text">
            <p>关注我们的社交媒体账号，获取最新优惠活动和医美资讯！</p>
            <p>每周更新医美知识、案例分享和专家问答，助您美丽蜕变！</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.contact-page {
  overflow-x: hidden;
}

.page-header {
  background-color: #2c2b7b;
  color: #fff;
  padding: 40px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb a {
  color: #fff;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.section-header p {
  font-size: 16px;
  color: #e74c3c;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 联系信息部分 */
.contact-info-section {
  padding: 60px 0;
}

.info-cards {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20px;
}

.info-card {
  flex: 0 0 calc(25% - 20px);
  background-color: #fff;
  border-radius: 10px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-icon {
  margin-bottom: 20px;
}

.info-icon img {
  width: 60px;
  height: 60px;
}

.info-card h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
}

.info-card p {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

/* 地图和留言部分 */
.map-message-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.map-message-content {
  display: flex;
  gap: 30px;
}

.map-container {
  flex: 0 0 55%;
}

.map-wrapper {
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.map-wrapper img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.map-info {
  padding: 20px;
}

.map-info h3 {
  font-size: 20px;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.transport-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.transport-item {
  flex: 0 0 calc(33.333% - 14px);
}

.transport-item h4 {
  font-size: 16px;
  color: #e74c3c;
  margin-bottom: 10px;
}

.transport-item p {
  font-size: 14px;
  color: #666;
  line-height: 1.6;
}

.message-container {
  flex: 0 0 45%;
}

.message-form {
  background-color: #fff;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  font-size: 16px;
  color: #333;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 14px;
}

.form-group textarea {
  height: 120px;
  resize: none;
}

.btn-submit {
  width: 100%;
  padding: 12px 0;
  background-color: #2c2b7b;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-submit:hover {
  background-color: #e74c3c;
}

/* 常见问题部分 */
.faq-section {
  padding: 60px 0;
}

.faq-list {
  max-width: 800px;
  margin: 0 auto;
}

.faq-item {
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.faq-question {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  cursor: pointer;
  background-color: #f9f9f9;
}

.faq-question h3 {
  font-size: 18px;
  color: #333;
  margin: 0;
}

.toggle-icon {
  font-size: 24px;
  color: #2c2b7b;
  font-weight: bold;
}

.faq-answer {
  padding: 0 20px 20px;
}

.faq-answer p {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 15px;
}

.faq-answer ol {
  padding-left: 20px;
}

.faq-answer li {
  font-size: 16px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 10px;
}

/* 关注我们部分 */
.follow-section {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.follow-content {
  display: flex;
  align-items: center;
  gap: 40px;
}

.qrcode-container {
  flex: 0 0 60%;
  display: flex;
  justify-content: space-around;
}

.qrcode-item {
  text-align: center;
}

.qrcode-item img {
  width: 150px;
  height: 150px;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.qrcode-item p {
  font-size: 16px;
  color: #333;
}

.social-text {
  flex: 0 0 40%;
}

.social-text p {
  font-size: 16px;
  color: #666;
  line-height: 1.8;
  margin-bottom: 20px;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .info-card {
    flex: 0 0 calc(50% - 20px);
  }
  
  .map-message-content {
    flex-direction: column;
  }
  
  .map-container,
  .message-container {
    flex: 0 0 100%;
  }
  
  .transport-item {
    flex: 0 0 calc(50% - 10px);
  }
  
  .follow-content {
    flex-direction: column;
  }
  
  .qrcode-container,
  .social-text {
    flex: 0 0 100%;
  }
  
  .social-text {
    text-align: center;
    margin-top: 20px;
  }
}

@media (max-width: 768px) {
  .info-card {
    flex: 0 0 100%;
    max-width: 400px;
    margin: 0 auto;
  }
  
  .transport-item {
    flex: 0 0 100%;
  }
  
  .qrcode-container {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 28px;
  }
}
</style>

<script>
export default {
  mounted() {
    // 添加FAQ点击展开/收起功能
    const faqItems = document.querySelectorAll('.faq-question');
    faqItems.forEach(item => {
      item.addEventListener('click', () => {
        const answer = item.nextElementSibling;
        const toggleIcon = item.querySelector('.toggle-icon');
        
        if (answer.style.display === 'block') {
          answer.style.display = 'none';
          toggleIcon.textContent = '+';
        } else {
          answer.style.display = 'block';
          toggleIcon.textContent = '-';
        }
      });
    });
    
    // 初始状态隐藏所有答案
    document.querySelectorAll('.faq-answer').forEach(answer => {
      answer.style.display = 'none';
    });
  }
}
</script> 