<template>
  <div class="news-page">
    <div class="page-header">
      <div class="container">
        <h1 class="reveal-on-scroll fade-in-down" data-once="true">新闻资讯</h1>
        <p class="reveal-on-scroll fade-in-up" data-once="true">了解凯丽思的最新动态、行业新闻和美丽资讯</p>
      </div>
    </div>

    <div class="container">
      <div class="news-content">
        <!-- 筛选和搜索 -->
        <aside class="filters-sidebar reveal-on-scroll fade-in-left" data-once="true">
          <div class="filter-group">
            <h3>搜索新闻</h3>
            <div class="search-box">
              <input type="text" v-model="searchQuery" placeholder="输入关键词..." />
              <button @click="performSearch">搜索</button>
            </div>
          </div>
          <div class="filter-group">
            <h3>新闻分类</h3>
            <ul class="category-list">
              <li 
                v-for="category in categories" 
                :key="category.id"
                :class="{ active: activeCategory === category.id }"
                @click="filterByCategory(category.id)"
              >
                {{ category.name }}
              </li>
            </ul>
          </div>
          <div class="filter-group">
            <h3>热门标签</h3>
            <div class="tags-cloud">
              <span v-for="tag in tags" :key="tag" class="tag">{{ tag }}</span>
            </div>
          </div>
        </aside>

        <!-- 新闻列表 -->
        <main class="news-list-section">
          <div v-if="filteredNews.length" class="news-grid">
            <div 
              v-for="(newsItem, index) in paginatedNews" 
              :key="newsItem.id"
              class="news-card reveal-on-scroll fade-in-up"
              :style="{ transitionDelay: `${index * 100}ms` }"
              data-once="true"
            >
              <div class="card-image">
                <img :src="newsItem.image" :alt="newsItem.title" />
                <div class="card-image-overlay"></div>
                <span class="news-date">{{ newsItem.date }}</span>
              </div>
              <div class="card-content">
                <h3 class="card-title">{{ newsItem.title }}</h3>
                <p class="card-excerpt">{{ newsItem.excerpt }}</p>
                <NuxtLink :to="`/news/${newsItem.id}`" class="btn-read-more ripple">
                  <span>阅读全文</span>
                </NuxtLink>
              </div>
            </div>
          </div>
          <div v-else class="no-results">
            <p>没有找到相关的新闻资讯。</p>
          </div>
          
          <!-- 分页 -->
          <div v-if="totalPages > 1" class="pagination">
            <button @click="prevPage" :disabled="currentPage === 1">上一页</button>
            <span>第 {{ currentPage }} 页 / 共 {{ totalPages }} 页</span>
            <button @click="nextPage" :disabled="currentPage === totalPages">下一页</button>
          </div>
        </main>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

// 模拟新闻数据
const news = ref([
  { id: 1, title: '凯丽思荣获"年度最佳医美机构"奖', excerpt: '在近日举行的亚洲医美行业年度盛典上，凯丽思凭借其卓越的服务质量和技术创新，荣获"年度最佳医美机构"大奖。', image: '/placeholder.jpg', date: '2024-07-15', category: 'hospital' },
  { id: 2, title: '夏季护肤全攻略：专家教你如何防晒与修复', excerpt: '炎炎夏日，如何保护我们的皮肤免受紫外线的伤害？凯丽思皮肤科专家为您带来最全面的夏季护肤指南。', image: '/placeholder.jpg', date: '2024-07-10', category: 'beauty' },
  { id: 3, title: '专访张教授：面部年轻化的新趋势', excerpt: '我们有幸邀请到著名的面部整形专家张教授，与我们分享当前面部年轻化领域的最新技术和发展趋势。', image: '/placeholder.jpg', date: '2024-07-05', category: 'technology' },
  { id: 4, title: '凯丽思引进最新一代热玛吉设备', excerpt: '为了提供更优质的抗衰服务，凯丽思近日引进了最新一代热玛吉Thermage FLX设备，为顾客带来更舒适、更有效的紧肤体验。', image: '/placeholder.jpg', date: '2024-06-28', category: 'technology' },
  { id: 5, title: '如何选择适合自己的双眼皮手术？', excerpt: '双眼皮手术是目前最受欢迎的整形项目之一。如何根据自己的眼型和面部特征选择最适合的手术方案？听听专家的建议。', image: '/placeholder.jpg', date: '2024-06-20', category: 'projects' },
  { id: 6, title: '凯丽思公益行：为山区儿童送去温暖', excerpt: '凯丽思医疗美容医院组织员工前往山区，为当地儿童送去学习用品和生活物资，积极履行社会责任。', image: '/placeholder.jpg', date: '2024-06-15', category: 'hospital' },
  { id: 7, title: '玻尿酸填充：你所需要知道的一切', excerpt: '玻尿酸作为微整形的明星产品，备受追捧。本文将详细解答关于玻尿酸的常见问题，帮助您做出明智的选择。', image: '/placeholder.jpg', date: '2024-06-10', category: 'projects' },
  { id: 8, title: '美丽科普：什么是光子嫩肤？', excerpt: '光子嫩肤作为一种先进的皮肤美容技术，能有效改善多种皮肤问题。本文将带您深入了解其原理、效果和注意事项。', image: '/placeholder.jpg', date: '2024-06-01', category: 'beauty' },
]);

// 分类数据
const categories = ref([
  { id: 'all', name: '全部新闻' },
  { id: 'hospital', name: '医院动态' },
  { id: 'beauty', name: '美丽课堂' },
  { id: 'technology', name: '前沿技术' },
  { id: 'projects', name: '项目解析' },
]);

// 热门标签
const tags = ref(['双眼皮', '玻尿酸', '热玛吉', '抗衰老', '皮肤管理', '微整形', '优惠活动']);

// 筛选和分页逻辑
const searchQuery = ref('');
const activeCategory = ref('all');
const currentPage = ref(1);
const itemsPerPage = 6;

const filteredNews = computed(() => {
  let result = news.value;
  if (activeCategory.value !== 'all') {
    result = result.filter(item => item.category === activeCategory.value);
  }
  if (searchQuery.value) {
    result = result.filter(item => item.title.includes(searchQuery.value) || item.excerpt.includes(searchQuery.value));
  }
  return result;
});

const totalPages = computed(() => {
  return Math.ceil(filteredNews.value.length / itemsPerPage);
});

const paginatedNews = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage;
  const end = start + itemsPerPage;
  return filteredNews.value.slice(start, end);
});

function filterByCategory(categoryId) {
  activeCategory.value = categoryId;
  currentPage.value = 1;
}

function performSearch() {
  currentPage.value = 1;
  // 实际项目中可能会调用API
}

function prevPage() {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
}

function nextPage() {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
}

</script>

<style scoped>
.news-page {
  background: #f8f9fa;
  padding-bottom: 80px;
}

.page-header {
  padding: 80px 0;
  text-align: center;
  background: linear-gradient(135deg, #2c2b7b, #e74c3c);
  color: #fff;
  margin-bottom: 60px;
}

.page-header h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 15px;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
}

.news-content {
  display: flex;
  gap: 40px;
}

/* 侧边栏 */
.filters-sidebar {
  flex: 0 0 300px;
}

.filter-group {
  background: #fff;
  padding: 25px;
  border-radius: 15px;
  margin-bottom: 30px;
  box-shadow: 0 5px 25px rgba(0,0,0,0.05);
}

.filter-group h3 {
  font-size: 20px;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.filter-group h3::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 40px;
  height: 3px;
  background: #e74c3c;
}

.search-box {
  display: flex;
}

.search-box input {
  flex-grow: 1;
  border: 1px solid #ddd;
  padding: 10px 15px;
  border-radius: 8px 0 0 8px;
  outline: none;
  transition: border-color 0.3s;
}

.search-box input:focus {
  border-color: #2c2b7b;
}

.search-box button {
  padding: 10px 15px;
  border: none;
  background: #2c2b7b;
  color: #fff;
  cursor: pointer;
  border-radius: 0 8px 8px 0;
  transition: background 0.3s;
}

.search-box button:hover {
  background: #e74c3c;
}

.category-list {
  list-style: none;
}

.category-list li {
  padding: 12px 15px;
  margin: 5px 0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.category-list li:hover,
.category-list li.active {
  background: #2c2b7b;
  color: #fff;
  transform: translateX(5px);
}

.tags-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  background: #f0f0f0;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s;
}

.tag:hover {
  background: #e74c3c;
  color: #fff;
  transform: scale(1.05);
}

/* 新闻列表 */
.news-list-section {
  flex-grow: 1;
}

.news-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.news-card {
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.07);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.news-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.12);
}

.card-image {
  height: 220px;
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.4s ease;
}

.news-card:hover .card-image img {
  transform: scale(1.05);
}

.card-image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to top, rgba(0,0,0,0.6), transparent);
}

.news-date {
  position: absolute;
  top: 15px;
  right: 15px;
  background: rgba(231, 76, 60, 0.9);
  color: #fff;
  padding: 5px 10px;
  font-size: 13px;
  border-radius: 8px;
}

.card-content {
  padding: 25px;
}

.card-title {
  font-size: 20px;
  margin-bottom: 15px;
  color: #333;
  line-height: 1.4;
}

.card-excerpt {
  font-size: 15px;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
}

.btn-read-more {
  display: inline-block;
  color: #e74c3c;
  font-weight: 600;
  text-decoration: none;
  position: relative;
}

.btn-read-more::after {
  content: '→';
  margin-left: 8px;
  transition: margin-left 0.3s;
}

.btn-read-more:hover::after {
  margin-left: 15px;
}

.no-results {
  text-align: center;
  padding: 50px;
  background: #fff;
  border-radius: 15px;
}

.pagination {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.pagination button {
  padding: 10px 20px;
  border: 1px solid #ddd;
  background: #fff;
  cursor: pointer;
  border-radius: 8px;
  transition: all 0.3s;
}

.pagination button:hover:not(:disabled) {
  background: #2c2b7b;
  color: #fff;
  border-color: #2c2b7b;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式 */
@media (max-width: 992px) {
  .news-content {
    flex-direction: column;
  }
  .filters-sidebar {
    flex: 0 0 auto;
  }
}

@media (max-width: 768px) {
  .news-grid {
    grid-template-columns: 1fr;
  }
}
</style> 