<template>
  <div class="layout">
    <header class="header">
      <div class="header-top">
        <div class="container">
          <div class="logo">
            <img src="/placeholder.jpg" alt="凯丽思" />
            <div class="logo-text">
              <h1>凯丽思医疗美容</h1>
              <p>三级整形外科医院 上市医美</p>
              <p>放心医美·我选凯丽思 凯丽思股票 (835533)</p>
            </div>
          </div>
          <div class="header-right">
            <div class="certification">
              <h2>AAAAA级医美机构</h2>
              <p>评审: 中国整形美容协会</p>
            </div>
            <div class="contact">
              <img src="/placeholder.jpg" alt="电话" />
              <div>
                <p>拨打咨询热线</p>
                <h3>0851-8686</h3>
              </div>
            </div>
          </div>
        </div>
      </div>
      <nav class="main-nav">
        <div class="container">
          <ul class="nav-list">
            <li><NuxtLink to="/">网站首页</NuxtLink></li>
            <li><NuxtLink to="/about">医院概况</NuxtLink></li>
            <li><NuxtLink to="/doctors">医生团队</NuxtLink></li>
            <li><NuxtLink to="/news">新闻资讯</NuxtLink></li>
            <li><NuxtLink to="/departments">科室导航</NuxtLink></li>
            <li><NuxtLink to="/qualification">医院资质</NuxtLink></li>
            <li><NuxtLink to="/contact">联系我们</NuxtLink></li>
          </ul>
        </div>
      </nav>
    </header>

    <main class="main-content">
      <slot />
    </main>

    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-logo">
            <img src="/placeholder.jpg" alt="凯丽思" />
            <p>凯丽思医疗美容医院</p>
          </div>
          <div class="footer-info">
            <p>地址: 贵州省贵阳市南明区花果园金融街1号</p>
            <p>电话: 0851-8686</p>
            <p>邮箱: <EMAIL></p>
          </div>
          <div class="footer-qrcode">
            <img src="/placeholder.jpg" alt="微信二维码" />
            <p>扫码关注我们</p>
          </div>
        </div>
        <div class="copyright">
          <p>© {{ new Date().getFullYear() }} 凯丽思医疗美容医院 版权所有</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<style scoped>
.layout {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.header {
  background-color: #fff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-top {
  padding: 15px 0;
}

.header-top .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
}

.logo img {
  height: 80px;
  margin-right: 15px;
}

.logo-text h1 {
  font-size: 24px;
  color: #333;
  margin-bottom: 5px;
}

.logo-text p {
  font-size: 14px;
  color: #666;
  margin: 2px 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.certification {
  margin-right: 30px;
  text-align: right;
}

.certification h2 {
  font-size: 20px;
  color: #333;
  margin-bottom: 5px;
}

.certification p {
  font-size: 14px;
  color: #666;
}

.contact {
  display: flex;
  align-items: center;
}

.contact img {
  height: 40px;
  margin-right: 10px;
}

.contact p {
  font-size: 14px;
  color: #666;
}

.contact h3 {
  font-size: 22px;
  color: #e74c3c;
}

.main-nav {
  background-color: #2c2b7b;
}

.nav-list {
  display: flex;
  list-style: none;
  justify-content: space-between;
}

.nav-list li {
  flex: 1;
  text-align: center;
}

.nav-list a {
  display: block;
  padding: 15px 0;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  transition: background-color 0.3s;
}

.nav-list a:hover,
.nav-list a.router-link-active {
  background-color: #e74c3c;
}

.main-content {
  flex: 1;
}

.footer {
  background-color: #2c2b7b;
  color: #fff;
  padding: 40px 0 20px;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  margin-bottom: 30px;
}

.footer-logo img {
  height: 60px;
  margin-bottom: 10px;
}

.footer-logo p {
  font-size: 16px;
}

.footer-info p {
  margin-bottom: 10px;
  font-size: 14px;
}

.footer-qrcode {
  text-align: center;
}

.footer-qrcode img {
  width: 120px;
  margin-bottom: 10px;
}

.copyright {
  text-align: center;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding-top: 20px;
  font-size: 14px;
}

@media (max-width: 768px) {
  .header-top .container {
    flex-direction: column;
  }
  
  .header-right {
    margin-top: 15px;
  }
  
  .nav-list {
    flex-wrap: wrap;
  }
  
  .nav-list li {
    flex: 0 0 33.333%;
  }
  
  .footer-content {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .footer-info,
  .footer-qrcode {
    margin-top: 20px;
  }
}
</style> 