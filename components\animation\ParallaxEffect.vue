<template>
  <div class="parallax-container" ref="parallaxContainer">
    <slot />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  speed: {
    type: Number,
    default: 0.5
  },
  direction: {
    type: String,
    default: 'vertical', // 'vertical', 'horizontal'
    validator: (value) => ['vertical', 'horizontal'].includes(value)
  },
  offset: {
    type: Number,
    default: 0
  }
});

const parallaxContainer = ref(null);
let ticking = false;

const updateParallax = () => {
  if (!parallaxContainer.value) return;
  
  const rect = parallaxContainer.value.getBoundingClientRect();
  const scrolled = window.pageYOffset;
  const rate = scrolled * -props.speed;
  
  if (props.direction === 'vertical') {
    parallaxContainer.value.style.transform = `translateY(${rate + props.offset}px)`;
  } else {
    parallaxContainer.value.style.transform = `translateX(${rate + props.offset}px)`;
  }
};

const requestTick = () => {
  if (!ticking) {
    requestAnimationFrame(updateParallax);
    ticking = true;
  }
};

const handleScroll = () => {
  requestTick();
  ticking = false;
};

onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true });
  updateParallax();
});

onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll);
});
</script>

<style scoped>
.parallax-container {
  will-change: transform;
}
</style>
