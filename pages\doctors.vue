<template>
  <div class="doctors-page">
    <!-- 页面标题 -->
    <div class="page-header">
      <div class="container">
        <h1>医生团队</h1>
        <div class="breadcrumb">
          <NuxtLink to="/">首页</NuxtLink> &gt; <span>医生团队</span>
        </div>
      </div>
    </div>

    <!-- 团队介绍 -->
    <div class="team-intro">
      <div class="container">
        <div class="section-header">
          <h2>专业团队</h2>
          <p>PROFESSIONAL TEAM</p>
        </div>
        <div class="intro-text">
          <p>凯丽思医疗美容医院拥有一支由国内外顶尖医美专家组成的专业团队，他们均拥有丰富的临床经验和精湛的技术水平。我们的医生团队不仅具备扎实的专业知识，还定期参加国际医美学术交流，掌握最前沿的医美技术和理念。</p>
          <p>我们的医生团队秉承"以人为本，以美为魂"的服务理念，致力于为每位顾客提供安全、专业、个性化的医疗美容服务，帮助您实现美丽蜕变。</p>
        </div>
      </div>
    </div>

    <!-- 专家团队 -->
    <div class="expert-team">
      <div class="container">
        <div class="section-header">
          <h2>专家团队</h2>
          <p>EXPERT TEAM</p>
        </div>
        <div class="team-grid">
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="张教授" />
            </div>
            <div class="doctor-info">
              <h3>张教授</h3>
              <p class="doctor-title">面部整形专家</p>
              <p class="doctor-exp">20年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院首席面部整形专家，中国整形美容协会会员，曾在韩国进修学习面部精细整形技术。</p>
                <p>擅长项目：面部轮廓塑造、面部年轻化、鼻部精细整形</p>
              </div>
              <NuxtLink to="/doctors/zhang" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="李医生" />
            </div>
            <div class="doctor-info">
              <h3>李医生</h3>
              <p class="doctor-title">眼部整形专家</p>
              <p class="doctor-exp">15年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院眼部整形中心主任，毕业于北京协和医学院，曾在日本进修眼部精细整形技术。</p>
                <p>擅长项目：韩式双眼皮、眼袋去除、眼周年轻化</p>
              </div>
              <NuxtLink to="/doctors/li" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="王医生" />
            </div>
            <div class="doctor-info">
              <h3>王医生</h3>
              <p class="doctor-title">微整形专家</p>
              <p class="doctor-exp">12年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院微整形中心主任，毕业于上海交通大学医学院，曾在美国进修微整形注射技术。</p>
                <p>擅长项目：玻尿酸注射、肉毒素注射、水光注射</p>
              </div>
              <NuxtLink to="/doctors/wang" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="刘医生" />
            </div>
            <div class="doctor-info">
              <h3>刘医生</h3>
              <p class="doctor-title">形体塑造专家</p>
              <p class="doctor-exp">10年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院形体塑造中心主任，毕业于中国医科大学，曾在韩国进修吸脂塑形技术。</p>
                <p>擅长项目：精准吸脂、自体脂肪移植、腰腹部塑形</p>
              </div>
              <NuxtLink to="/doctors/liu" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="陈医生" />
            </div>
            <div class="doctor-info">
              <h3>陈医生</h3>
              <p class="doctor-title">皮肤美容专家</p>
              <p class="doctor-exp">8年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院皮肤美容中心主任，毕业于广州医科大学，曾在法国进修皮肤抗衰老技术。</p>
                <p>擅长项目：激光美肤、祛斑祛痘、皮肤抗衰</p>
              </div>
              <NuxtLink to="/doctors/chen" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
          <div class="doctor-card">
            <div class="doctor-image">
              <img src="/placeholder.jpg" alt="赵医生" />
            </div>
            <div class="doctor-info">
              <h3>赵医生</h3>
              <p class="doctor-title">口腔美容专家</p>
              <p class="doctor-exp">9年临床经验</p>
              <div class="doctor-desc">
                <p>凯丽思医疗美容医院口腔美容中心主任，毕业于武汉大学口腔医学院，曾在美国进修口腔美学技术。</p>
                <p>擅长项目：牙齿美白、牙齿矫正、全瓷牙冠</p>
              </div>
              <NuxtLink to="/doctors/zhao" class="btn-more">查看详情</NuxtLink>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 专家咨询 -->
    <div class="consultation-section">
      <div class="container">
        <div class="consultation-content">
          <h2>预约专家咨询</h2>
          <p>我们的专家将为您提供一对一的个性化美丽方案</p>
          <form class="consultation-form">
            <div class="form-group">
              <input type="text" placeholder="您的姓名" />
            </div>
            <div class="form-group">
              <input type="tel" placeholder="您的电话" />
            </div>
            <div class="form-group">
              <select>
                <option value="">选择专家</option>
                <option value="zhang">张教授-面部整形</option>
                <option value="li">李医生-眼部整形</option>
                <option value="wang">王医生-微整形</option>
                <option value="liu">刘医生-形体塑造</option>
                <option value="chen">陈医生-皮肤美容</option>
                <option value="zhao">赵医生-口腔美容</option>
              </select>
            </div>
            <button type="submit" class="btn-submit">立即预约</button>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.doctors-page {
  overflow-x: hidden;
}

.page-header {
  background-color: #2c2b7b;
  color: #fff;
  padding: 40px 0;
  text-align: center;
}

.page-header h1 {
  font-size: 36px;
  margin-bottom: 10px;
}

.breadcrumb {
  font-size: 14px;
}

.breadcrumb a {
  color: #fff;
  text-decoration: none;
}

.breadcrumb a:hover {
  text-decoration: underline;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  text-align: center;
  margin-bottom: 40px;
}

.section-header h2 {
  font-size: 32px;
  color: #333;
  margin-bottom: 10px;
}

.section-header p {
  font-size: 16px;
  color: #e74c3c;
  text-transform: uppercase;
  letter-spacing: 2px;
}

/* 团队介绍部分 */
.team-intro {
  padding: 60px 0;
}

.intro-text {
  max-width: 800px;
  margin: 0 auto;
  text-align: center;
}

.intro-text p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
  line-height: 1.8;
}

/* 专家团队部分 */
.expert-team {
  padding: 60px 0;
  background-color: #f9f9f9;
}

.team-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}

.doctor-card {
  display: flex;
  background-color: #fff;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.doctor-card:hover {
  transform: translateY(-5px);
}

.doctor-image {
  flex: 0 0 40%;
  overflow: hidden;
}

.doctor-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s;
}

.doctor-card:hover .doctor-image img {
  transform: scale(1.05);
}

.doctor-info {
  flex: 0 0 60%;
  padding: 25px;
}

.doctor-info h3 {
  font-size: 22px;
  color: #333;
  margin-bottom: 5px;
}

.doctor-title {
  font-size: 16px;
  color: #e74c3c;
  margin-bottom: 5px;
}

.doctor-exp {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.doctor-desc {
  margin-bottom: 20px;
}

.doctor-desc p {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
  line-height: 1.6;
}

.btn-more {
  display: inline-block;
  padding: 8px 20px;
  background-color: #2c2b7b;
  color: #fff;
  text-decoration: none;
  border-radius: 5px;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-more:hover {
  background-color: #e74c3c;
}

/* 专家咨询部分 */
.consultation-section {
  padding: 60px 0;
  background-color: #2c2b7b;
  color: #fff;
}

.consultation-content {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
}

.consultation-content h2 {
  font-size: 32px;
  margin-bottom: 15px;
}

.consultation-content p {
  font-size: 16px;
  margin-bottom: 30px;
}

.consultation-form {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  justify-content: center;
}

.form-group {
  flex: 0 0 calc(33.333% - 10px);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 5px;
  font-size: 14px;
}

.btn-submit {
  flex: 0 0 100%;
  margin-top: 15px;
  padding: 12px 30px;
  background-color: #e74c3c;
  color: #fff;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-submit:hover {
  background-color: #c0392b;
}

/* 响应式样式 */
@media (max-width: 992px) {
  .team-grid {
    grid-template-columns: 1fr;
  }
  
  .doctor-card {
    max-width: 600px;
    margin: 0 auto;
  }
}

@media (max-width: 768px) {
  .doctor-card {
    flex-direction: column;
  }
  
  .doctor-image {
    flex: 0 0 250px;
  }
  
  .doctor-info {
    flex: 0 0 auto;
  }
  
  .form-group {
    flex: 0 0 100%;
  }
}

@media (max-width: 576px) {
  .page-header h1 {
    font-size: 28px;
  }
  
  .doctor-image {
    flex: 0 0 200px;
  }
}
</style> 