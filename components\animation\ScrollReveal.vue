<script setup>
import { onMounted, onUnmounted } from 'vue';

// 观察器配置
const observerOptions = {
  root: null, // 使用视口作为根
  rootMargin: '0px',
  threshold: 0.1 // 当元素10%进入视口时触发
};

// 处理元素进入视口
const handleIntersect = (entries, observer) => {
  entries.forEach(entry => {
    if (entry.isIntersecting) {
      // 添加显示类
      entry.target.classList.add('revealed');
      
      // 如果只需要触发一次动画，可以取消观察
      if (entry.target.dataset.once === 'true') {
        observer.unobserve(entry.target);
      }
    } else if (entry.target.dataset.once !== 'true') {
      // 如果不是一次性动画，离开视口时移除类
      entry.target.classList.remove('revealed');
    }
  });
};

let observer = null;

onMounted(() => {
  // 确保 IntersectionObserver API 可用
  if ('IntersectionObserver' in window) {
    observer = new IntersectionObserver(handleIntersect, observerOptions);
    
    // 获取所有需要观察的元素
    const elements = document.querySelectorAll('.reveal-on-scroll');
    elements.forEach(el => observer.observe(el));
  } else {
    // 如果 IntersectionObserver 不可用，为所有元素添加显示类
    const elements = document.querySelectorAll('.reveal-on-scroll');
    elements.forEach(el => el.classList.add('revealed'));
  }
});

onUnmounted(() => {
  // 清理观察器
  if (observer) {
    observer.disconnect();
  }
});
</script>

<template>
  <!-- 这是一个功能性组件，不需要渲染任何内容 -->
</template> 