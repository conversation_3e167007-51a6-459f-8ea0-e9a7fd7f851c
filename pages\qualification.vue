<template>
  <div class="qualification-page">
    <div class="page-header">
      <div class="container">
        <h1 class="reveal-on-scroll fade-in-down" data-once="true">医院资质</h1>
        <p class="reveal-on-scroll fade-in-up" data-once="true">专业认证，权威认可，实力见证</p>
      </div>
    </div>

    <div class="container">
      <div class="qualification-grid">
        <div 
          v-for="(qual, index) in qualifications" 
          :key="qual.id" 
          class="qual-card reveal-on-scroll fade-in-up"
          :style="{ transitionDelay: `${index * 100}ms` }"
          @click="openModal(qual)"
          data-once="true"
        >
          <div class="qual-image">
            <img :src="qual.image" :alt="qual.title" />
            <div class="image-overlay">
              <span class="view-icon">+</span>
            </div>
          </div>
          <div class="qual-info">
            <h3 class="qual-title">{{ qual.title }}</h3>
            <p class="qual-issuer">颁发机构: {{ qual.issuer }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 模态框 -->
    <div v-if="selectedQual" class="modal" @click="closeModal">
      <div class="modal-content" @click.stop>
        <span class="close-btn" @click="closeModal">&times;</span>
        <img :src="selectedQual.image" :alt="selectedQual.title" />
        <h3>{{ selectedQual.title }}</h3>
        <p>颁发机构: {{ selectedQual.issuer }}</p>
        <p>颁发日期: {{ selectedQual.date }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const qualifications = ref([
  { id: 1, title: '医疗机构执业许可证', issuer: '中华人民共和国国家卫生健康委员会', date: '2023-01-15', image: '/placeholder.jpg' },
  { id: 2, title: 'AAAAA级医疗美容机构认证', issuer: '中国整形美容协会', date: '2023-03-20', image: '/placeholder.jpg' },
  { id: 3, title: '三级整形外科医院认证', issuer: '省卫生健康委员会', date: '2022-11-10', image: '/placeholder.jpg' },
  { id: 4, title: 'ISO 9001 质量管理体系认证', issuer: '国际标准化组织', date: '2023-05-30', image: '/placeholder.jpg' },
  { id: 5, 'title': '消费者信赖的医美品牌奖', 'issuer': '中国消费者协会', 'date': '2023-09-09', 'image': '/placeholder.jpg' },
  { id: 6, 'title': '年度安全医美示范单位', 'issuer': '中华医学会医学美学与美容学分会', 'date': '2023-11-20', 'image': '/placeholder.jpg' },
  { id: 7, 'title': '热玛吉官方授权合作机构', 'issuer': 'Solta Medical', 'date': '2023-02-18', 'image': '/placeholder.jpg' },
  { id: 8, 'title': '乔雅登玻尿酸指定注射机构', 'issuer': '艾尔建（Allergan）', 'date': '2023-04-25', 'image': '/placeholder.jpg' }
]);

const selectedQual = ref(null);

function openModal(qual) {
  selectedQual.value = qual;
}

function closeModal() {
  selectedQual.value = null;
}
</script>

<style scoped>
.qualification-page {
  background: #f8f9fa;
  padding-bottom: 80px;
}

.page-header {
  padding: 80px 0;
  text-align: center;
  background: linear-gradient(135deg, #2c2b7b, #e74c3c);
  color: #fff;
  margin-bottom: 60px;
}

.page-header h1 {
  font-size: 48px;
  font-weight: 700;
  margin-bottom: 15px;
}

.page-header p {
  font-size: 18px;
  opacity: 0.9;
}

.qualification-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

.qual-card {
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 8px 30px rgba(0,0,0,0.07);
  overflow: hidden;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
}

.qual-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 40px rgba(0,0,0,0.14);
}

.qual-image {
  position: relative;
  height: 200px;
}

.qual-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.qual-card:hover .image-overlay {
  opacity: 1;
}

.view-icon {
  color: #fff;
  font-size: 48px;
  font-weight: bold;
}

.qual-info {
  padding: 20px;
  text-align: center;
}

.qual-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: #333;
}

.qual-issuer {
  font-size: 14px;
  color: #777;
}

/* 模态框样式 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
  animation: fadeIn 0.3s ease;
}

.modal-content {
  position: relative;
  background: #fff;
  padding: 30px;
  border-radius: 10px;
  max-width: 90vw;
  max-height: 90vh;
  text-align: center;
  animation: fadeInScale 0.3s ease;
}

.modal-content img {
  max-width: 100%;
  max-height: 70vh;
  margin-bottom: 20px;
  border-radius: 5px;
}

.close-btn {
  position: absolute;
  top: 15px;
  right: 20px;
  font-size: 30px;
  color: #555;
  cursor: pointer;
  transition: transform 0.2s;
}

.close-btn:hover {
  transform: scale(1.2);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInScale {
  from { transform: scale(0.9); opacity: 0; }
  to { transform: scale(1); opacity: 1; }
}
</style> 