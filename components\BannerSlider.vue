<template>
  <div class="banner-slider">
    <Swiper
      :modules="[SwiperNavigation, SwiperPagination, SwiperAutoplay, SwiperEffectFade, SwiperParallax]"
      :slides-per-view="1"
      :space-between="0"
      :loop="true"
      :autoplay="{
        delay: 5000,
        disableOnInteraction: false,
      }"
      :pagination="{
        clickable: true,
        dynamicBullets: true,
      }"
      :navigation="true"
      effect="fade"
      :fade-effect="{
        crossFade: true
      }"
      :parallax="true"
      class="my-swiper"
    >
      <SwiperSlide v-for="(slide, index) in slides" :key="index">
        <div class="slide-content">
          <img :src="slide.image" class="slide-bg" alt="Banner Image" />
          <div class="slide-overlay"></div>
          <div class="container">
            <div class="text-content">
              <h2 data-swiper-parallax="-300">{{ slide.title }}</h2>
              <h3 data-swiper-parallax="-200">{{ slide.subtitle }}</h3>
              <p data-swiper-parallax="-100">{{ slide.description }}</p>
              <NuxtLink :to="slide.link" class="btn-banner ripple" data-swiper-parallax="-50">
                <span>{{ slide.buttonText }}</span>
              </NuxtLink>
            </div>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
  </div>
</template>

<script setup>
const props = defineProps({
  slides: {
    type: Array,
    required: true,
  },
});
</script>

<style scoped>
.banner-slider {
  position: relative;
  width: 100%;
  height: 600px;
  overflow: hidden;
  background: #f0f0f0;
}

.my-swiper {
  width: 100%;
  height: 100%;
}

.slide-content {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slide-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.05);
  transition: transform 8s ease-out;
}

.swiper-slide-active .slide-bg {
  transform: scale(1);
}

.slide-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(44, 43, 123, 0.6) 0%, rgba(231, 76, 60, 0.4) 100%);
  z-index: 1;
}

.container {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.text-content {
  color: #fff;
  max-width: 600px;
}

.text-content h2 {
  font-size: 52px;
  font-weight: 700;
  margin-bottom: 15px;
  text-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);
}

.text-content h3 {
  font-size: 36px;
  font-weight: 500;
  margin-bottom: 20px;
  opacity: 0.9;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.text-content p {
  font-size: 18px;
  line-height: 1.6;
  margin-bottom: 30px;
  max-width: 500px;
  opacity: 0.95;
}

.btn-banner {
  display: inline-block;
  padding: 12px 35px;
  background: linear-gradient(45deg, #fff, #f8f9fa);
  color: #2c2b7b;
  text-decoration: none;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.btn-banner:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  color: #e74c3c;
}

.btn-banner span {
  position: relative;
  z-index: 2;
}

:deep(.swiper-pagination-bullet) {
  background-color: rgba(255, 255, 255, 0.7);
  width: 10px;
  height: 10px;
  transition: all 0.3s ease;
}

:deep(.swiper-pagination-bullet-active) {
  background-color: #fff;
  width: 25px;
  border-radius: 5px;
}

:deep(.swiper-button-next),
:deep(.swiper-button-prev) {
  color: #fff;
  width: 50px;
  height: 50px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
}
:deep(.swiper-button-next:hover),
:deep(.swiper-button-prev:hover) {
  background: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
}

:deep(.swiper-button-next:after),
:deep(.swiper-button-prev:after) {
  font-size: 20px;
  font-weight: bold;
}

@media (max-width: 768px) {
  .banner-slider {
    height: 500px;
  }
  .text-content h2 {
    font-size: 38px;
  }
  .text-content h3 {
    font-size: 28px;
  }
  .text-content p {
    font-size: 16px;
  }
}
</style>
